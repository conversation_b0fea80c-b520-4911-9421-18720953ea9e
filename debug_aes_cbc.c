/*
 * debug_aes_cbc.c
 * 
 * AES CBC Mode Debug Tool
 * 
 * This program provides detailed debugging information for AES CBC encryption/decryption
 * including step-by-step analysis of the CBC process.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "include/aes_cbc.h"
#include "include/aes.h"
#include "include/cipher.h"

// External declaration of AES CBC cipher type
extern cipher_type_t aes_cbc;

// Debug flag - set to 1 to enable detailed debugging
#define DEBUG_VERBOSE 1

void print_hex_block(const char* label, const unsigned char* data, int len) {
    printf("%s: ", label);
    for (int i = 0; i < len; i++) {
        printf("%02x", data[i]);
        if ((i + 1) % 4 == 0) printf(" ");
    }
    printf("\n");
}

void print_separator(const char* title) {
    printf("\n");
    printf("=== %s ===\n", title);
    printf("\n");
}

void print_block_analysis(const char* stage, const unsigned char* block, int block_num) {
    printf("  Block %d %s: ", block_num, stage);
    for (int i = 0; i < 16; i++) {
        printf("%02x", block[i]);
        if ((i + 1) % 4 == 0) printf(" ");
    }
    printf("\n");
}

void debug_cbc_encryption_manual(const unsigned char* key, const unsigned char* iv, 
                                const unsigned char* plaintext, int plaintext_len) {
    print_separator("MANUAL CBC ENCRYPTION DEBUG");
    
    printf("Input Parameters:\n");
    print_hex_block("Key", key, 16);
    print_hex_block("IV", iv, 16);
    print_hex_block("Plaintext", plaintext, plaintext_len);
    printf("Plaintext length: %d bytes\n", plaintext_len);
    
    // Calculate padding
    int padding_needed = 16 - (plaintext_len % 16);
    if (padding_needed == 0) padding_needed = 16; // Always add at least one padding block
    int padded_len = plaintext_len + padding_needed;
    
    printf("\nPadding Analysis:\n");
    printf("Original length: %d bytes\n", plaintext_len);
    printf("Padding needed: %d bytes\n", padding_needed);
    printf("Padded length: %d bytes\n", padded_len);
    printf("Number of blocks: %d\n", padded_len / 16);
    
    // Create padded plaintext
    unsigned char* padded_plaintext = malloc(padded_len);
    memcpy(padded_plaintext, plaintext, plaintext_len);
    
    // Add PKCS#7 padding
    for (int i = plaintext_len; i < padded_len; i++) {
        padded_plaintext[i] = padding_needed;
    }
    
    printf("\nAfter PKCS#7 Padding:\n");
    print_hex_block("Padded plaintext", padded_plaintext, padded_len);
    
    // Expand AES key
    aes_expanded_key_t expanded_key;
    v128_t key_v128;
    memcpy(&key_v128, key, 16);
    aes_expand_encryption_key(&key_v128, expanded_key);
    
    printf("\nAES Key Expansion completed.\n");
    
    // CBC encryption process
    printf("\nCBC Encryption Process:\n");
    unsigned char previous_block[16];
    unsigned char current_block[16];
    unsigned char xor_result[16];
    unsigned char* ciphertext = malloc(padded_len);
    
    // Initialize previous block with IV
    memcpy(previous_block, iv, 16);
    print_hex_block("Initial previous block (IV)", previous_block, 16);
    
    for (int block = 0; block < padded_len / 16; block++) {
        printf("\n--- Block %d ---\n", block + 1);
        
        // Get current plaintext block
        memcpy(current_block, padded_plaintext + block * 16, 16);
        print_block_analysis("plaintext", current_block, block + 1);
        print_block_analysis("previous ciphertext", previous_block, block);
        
        // XOR with previous ciphertext (or IV for first block)
        for (int i = 0; i < 16; i++) {
            xor_result[i] = current_block[i] ^ previous_block[i];
        }
        print_block_analysis("after XOR", xor_result, block + 1);
        
        // AES encrypt
        v128_t block_v128;
        memcpy(&block_v128, xor_result, 16);
        aes_encrypt(&block_v128, expanded_key);
        memcpy(ciphertext + block * 16, &block_v128, 16);
        
        print_block_analysis("after AES encrypt", ciphertext + block * 16, block + 1);
        
        // Update previous block for next iteration
        memcpy(previous_block, ciphertext + block * 16, 16);
    }
    
    printf("\nFinal Result:\n");
    print_hex_block("Complete ciphertext", ciphertext, padded_len);
    
    free(padded_plaintext);
    free(ciphertext);
}

void debug_cbc_with_library(const unsigned char* key, const unsigned char* iv, 
                           const unsigned char* plaintext, int plaintext_len) {
    print_separator("LIBRARY CBC ENCRYPTION DEBUG");
    
    // Prepare data
    unsigned char data[512];
    memcpy(data, plaintext, plaintext_len);
    
    printf("Using library functions:\n");
    print_hex_block("Input key", key, 16);
    print_hex_block("Input IV", iv, 16);
    print_hex_block("Input plaintext", plaintext, plaintext_len);
    
    // Create cipher
    cipher_t *cipher;
    err_status_t status;
    
    status = cipher_type_alloc(&aes_cbc, &cipher, 16);
    if (status != err_status_ok) {
        printf("Error: Failed to allocate cipher (status: %d)\n", status);
        return;
    }
    printf("✓ Cipher allocated successfully\n");
    
    status = cipher_init(cipher, key, direction_encrypt);
    if (status != err_status_ok) {
        printf("Error: Failed to initialize cipher (status: %d)\n", status);
        cipher_dealloc(cipher);
        return;
    }
    printf("✓ Cipher initialized for encryption\n");
    
    status = cipher_set_iv(cipher, (void*)iv);
    if (status != err_status_ok) {
        printf("Error: Failed to set IV (status: %d)\n", status);
        cipher_dealloc(cipher);
        return;
    }
    printf("✓ IV set successfully\n");
    
    // Encrypt
    unsigned int len = plaintext_len;
    printf("\nBefore encryption: length = %u\n", len);
    
    status = cipher_encrypt(cipher, data, &len);
    if (status != err_status_ok) {
        printf("Error: Encryption failed (status: %d)\n", status);
        cipher_dealloc(cipher);
        return;
    }
    
    printf("After encryption: length = %u\n", len);
    print_hex_block("Library ciphertext", data, len);
    
    cipher_dealloc(cipher);
}

void debug_full_cycle(const unsigned char* key, const unsigned char* iv,
                     const unsigned char* plaintext, int plaintext_len) {
    print_separator("FULL ENCRYPTION/DECRYPTION CYCLE");

    unsigned char data[512];
    unsigned char decrypted[512];
    memcpy(data, plaintext, plaintext_len);

    printf("Original message: \"%.*s\"\n", plaintext_len, plaintext);
    print_hex_block("Original bytes", plaintext, plaintext_len);

    // Encryption
    cipher_t *cipher_enc;
    err_status_t status;

    status = cipher_type_alloc(&aes_cbc, &cipher_enc, 16);
    if (status != err_status_ok) {
        printf("Error: Failed to allocate encryption cipher\n");
        return;
    }

    status = cipher_init(cipher_enc, key, direction_encrypt);
    if (status != err_status_ok) {
        printf("Error: Failed to initialize encryption cipher\n");
        cipher_dealloc(cipher_enc);
        return;
    }

    status = cipher_set_iv(cipher_enc, (void*)iv);
    if (status != err_status_ok) {
        printf("Error: Failed to set IV for encryption\n");
        cipher_dealloc(cipher_enc);
        return;
    }

    unsigned int enc_len = plaintext_len;
    status = cipher_encrypt(cipher_enc, data, &enc_len);
    if (status != err_status_ok) {
        printf("Error: Encryption failed\n");
        cipher_dealloc(cipher_enc);
        return;
    }

    printf("\nAfter encryption:\n");
    printf("Encrypted length: %u bytes\n", enc_len);
    print_hex_block("Ciphertext", data, enc_len);

    cipher_dealloc(cipher_enc);

    // Decryption
    memcpy(decrypted, data, enc_len);

    cipher_t *cipher_dec;
    status = cipher_type_alloc(&aes_cbc, &cipher_dec, 16);
    if (status != err_status_ok) {
        printf("Error: Failed to allocate decryption cipher\n");
        return;
    }

    status = cipher_init(cipher_dec, key, direction_decrypt);
    if (status != err_status_ok) {
        printf("Error: Failed to initialize decryption cipher\n");
        cipher_dealloc(cipher_dec);
        return;
    }

    status = cipher_set_iv(cipher_dec, (void*)iv);
    if (status != err_status_ok) {
        printf("Error: Failed to set IV for decryption\n");
        cipher_dealloc(cipher_dec);
        return;
    }

    unsigned int dec_len = enc_len;
    status = cipher_decrypt(cipher_dec, decrypted, &dec_len);
    if (status != err_status_ok) {
        printf("Error: Decryption failed\n");
        cipher_dealloc(cipher_dec);
        return;
    }

    printf("\nAfter decryption:\n");
    printf("Decrypted length: %u bytes\n", dec_len);
    print_hex_block("Decrypted bytes", decrypted, dec_len);
    printf("Decrypted message: \"%.*s\"\n", dec_len, decrypted);

    // Verify
    if (dec_len == plaintext_len && memcmp(plaintext, decrypted, plaintext_len) == 0) {
        printf("✓ SUCCESS: Decryption matches original!\n");
    } else {
        printf("✗ FAILURE: Decryption does not match original!\n");
    }

    cipher_dealloc(cipher_dec);
}

int main() {
    printf("AES CBC Mode Debug Tool\n");
    printf("=======================\n");

    // Test vectors from NIST SP 800-38A
    unsigned char key[16] = {
        0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
        0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
    };

    unsigned char iv[16] = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f
    };

    printf("Debug Configuration:\n");
    print_hex_block("Key", key, 16);
    print_hex_block("IV", iv, 16);

    // Simple test first
    const char* simple_msg = "Hello World!";
    printf("\n");
    printf("########################################\n");
    printf("SIMPLE TEST: \"%s\"\n", simple_msg);
    printf("########################################\n");

    debug_full_cycle(key, iv, (const unsigned char*)simple_msg, strlen(simple_msg));

    // Interactive mode
    printf("\n");
    print_separator("INTERACTIVE MODE");
    printf("Enter your own message to debug (or press Enter to skip): ");

    char user_input[256];
    if (fgets(user_input, sizeof(user_input), stdin) && strlen(user_input) > 1) {
        user_input[strcspn(user_input, "\n")] = 0; // Remove newline

        printf("\n");
        printf("########################################\n");
        printf("USER TEST: \"%s\"\n", user_input);
        printf("########################################\n");

        debug_full_cycle(key, iv, (const unsigned char*)user_input, strlen(user_input));
    }

    print_separator("DEBUGGING COMPLETE");
    printf("Debug session finished!\n");
    printf("\nTo modify debug behavior:\n");
    printf("- Edit the key/IV values in the source code\n");
    printf("- Uncomment debug_cbc_encryption_manual() calls for detailed step analysis\n");
    printf("- Set DEBUG_VERBOSE to 1 for more detailed output\n");

    return 0;
}
