# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/opt/homebrew/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/opt/homebrew/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/opt/homebrew/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/opt/homebrew/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/opt/homebrew/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/opt/homebrew/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named cryptomodule

# Build rule for target.
cryptomodule: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cryptomodule
.PHONY : cryptomodule

# fast build rule for target.
cryptomodule/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/build
.PHONY : cryptomodule/fast

#=============================================================================
# Target rules for targets named aes_calc

# Build rule for target.
aes_calc: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 aes_calc
.PHONY : aes_calc

# fast build rule for target.
aes_calc/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_calc.dir/build.make CMakeFiles/aes_calc.dir/build
.PHONY : aes_calc/fast

#=============================================================================
# Target rules for targets named cipher_driver

# Build rule for target.
cipher_driver: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 cipher_driver
.PHONY : cipher_driver

# fast build rule for target.
cipher_driver/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cipher_driver.dir/build.make CMakeFiles/cipher_driver.dir/build
.PHONY : cipher_driver/fast

#=============================================================================
# Target rules for targets named datatypes_driver

# Build rule for target.
datatypes_driver: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 datatypes_driver
.PHONY : datatypes_driver

# fast build rule for target.
datatypes_driver/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/datatypes_driver.dir/build.make CMakeFiles/datatypes_driver.dir/build
.PHONY : datatypes_driver/fast

#=============================================================================
# Target rules for targets named stat_driver

# Build rule for target.
stat_driver: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 stat_driver
.PHONY : stat_driver

# fast build rule for target.
stat_driver/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stat_driver.dir/build.make CMakeFiles/stat_driver.dir/build
.PHONY : stat_driver/fast

#=============================================================================
# Target rules for targets named sha1_driver

# Build rule for target.
sha1_driver: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 sha1_driver
.PHONY : sha1_driver

# fast build rule for target.
sha1_driver/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sha1_driver.dir/build.make CMakeFiles/sha1_driver.dir/build
.PHONY : sha1_driver/fast

#=============================================================================
# Target rules for targets named kernel_driver

# Build rule for target.
kernel_driver: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 kernel_driver
.PHONY : kernel_driver

# fast build rule for target.
kernel_driver/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kernel_driver.dir/build.make CMakeFiles/kernel_driver.dir/build
.PHONY : kernel_driver/fast

#=============================================================================
# Target rules for targets named rand_gen

# Build rule for target.
rand_gen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 rand_gen
.PHONY : rand_gen

# fast build rule for target.
rand_gen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rand_gen.dir/build.make CMakeFiles/rand_gen.dir/build
.PHONY : rand_gen/fast

#=============================================================================
# Target rules for targets named env

# Build rule for target.
env: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 env
.PHONY : env

# fast build rule for target.
env/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/env.dir/build.make CMakeFiles/env.dir/build
.PHONY : env/fast

#=============================================================================
# Target rules for targets named aes_cbc_test

# Build rule for target.
aes_cbc_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 aes_cbc_test
.PHONY : aes_cbc_test

# fast build rule for target.
aes_cbc_test/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_test.dir/build.make CMakeFiles/aes_cbc_test.dir/build
.PHONY : aes_cbc_test/fast

#=============================================================================
# Target rules for targets named aes_cbc_interactive

# Build rule for target.
aes_cbc_interactive: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 aes_cbc_interactive
.PHONY : aes_cbc_interactive

# fast build rule for target.
aes_cbc_interactive/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_interactive.dir/build.make CMakeFiles/aes_cbc_interactive.dir/build
.PHONY : aes_cbc_interactive/fast

#=============================================================================
# Target rules for targets named debug_aes_cbc

# Build rule for target.
debug_aes_cbc: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 debug_aes_cbc
.PHONY : debug_aes_cbc

# fast build rule for target.
debug_aes_cbc/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_aes_cbc.dir/build.make CMakeFiles/debug_aes_cbc.dir/build
.PHONY : debug_aes_cbc/fast

#=============================================================================
# Target rules for targets named simple_debug

# Build rule for target.
simple_debug: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 simple_debug
.PHONY : simple_debug

# fast build rule for target.
simple_debug/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_debug.dir/build.make CMakeFiles/simple_debug.dir/build
.PHONY : simple_debug/fast

#=============================================================================
# Target rules for targets named runtest

# Build rule for target.
runtest: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 runtest
.PHONY : runtest

# fast build rule for target.
runtest/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/runtest.dir/build.make CMakeFiles/runtest.dir/build
.PHONY : runtest/fast

ae_xfm/xfm.o: ae_xfm/xfm.c.o
.PHONY : ae_xfm/xfm.o

# target to build an object file
ae_xfm/xfm.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.o
.PHONY : ae_xfm/xfm.c.o

ae_xfm/xfm.i: ae_xfm/xfm.c.i
.PHONY : ae_xfm/xfm.i

# target to preprocess a source file
ae_xfm/xfm.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.i
.PHONY : ae_xfm/xfm.c.i

ae_xfm/xfm.s: ae_xfm/xfm.c.s
.PHONY : ae_xfm/xfm.s

# target to generate assembly for a file
ae_xfm/xfm.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.s
.PHONY : ae_xfm/xfm.c.s

cipher/aes.o: cipher/aes.c.o
.PHONY : cipher/aes.o

# target to build an object file
cipher/aes.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/cipher/aes.c.o
.PHONY : cipher/aes.c.o

cipher/aes.i: cipher/aes.c.i
.PHONY : cipher/aes.i

# target to preprocess a source file
cipher/aes.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/cipher/aes.c.i
.PHONY : cipher/aes.c.i

cipher/aes.s: cipher/aes.c.s
.PHONY : cipher/aes.s

# target to generate assembly for a file
cipher/aes.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/cipher/aes.c.s
.PHONY : cipher/aes.c.s

cipher/aes_cbc.o: cipher/aes_cbc.c.o
.PHONY : cipher/aes_cbc.o

# target to build an object file
cipher/aes_cbc.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.o
.PHONY : cipher/aes_cbc.c.o

cipher/aes_cbc.i: cipher/aes_cbc.c.i
.PHONY : cipher/aes_cbc.i

# target to preprocess a source file
cipher/aes_cbc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.i
.PHONY : cipher/aes_cbc.c.i

cipher/aes_cbc.s: cipher/aes_cbc.c.s
.PHONY : cipher/aes_cbc.s

# target to generate assembly for a file
cipher/aes_cbc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.s
.PHONY : cipher/aes_cbc.c.s

cipher/aes_icm.o: cipher/aes_icm.c.o
.PHONY : cipher/aes_icm.o

# target to build an object file
cipher/aes_icm.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.o
.PHONY : cipher/aes_icm.c.o

cipher/aes_icm.i: cipher/aes_icm.c.i
.PHONY : cipher/aes_icm.i

# target to preprocess a source file
cipher/aes_icm.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.i
.PHONY : cipher/aes_icm.c.i

cipher/aes_icm.s: cipher/aes_icm.c.s
.PHONY : cipher/aes_icm.s

# target to generate assembly for a file
cipher/aes_icm.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.s
.PHONY : cipher/aes_icm.c.s

cipher/cipher.o: cipher/cipher.c.o
.PHONY : cipher/cipher.o

# target to build an object file
cipher/cipher.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/cipher/cipher.c.o
.PHONY : cipher/cipher.c.o

cipher/cipher.i: cipher/cipher.c.i
.PHONY : cipher/cipher.i

# target to preprocess a source file
cipher/cipher.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/cipher/cipher.c.i
.PHONY : cipher/cipher.c.i

cipher/cipher.s: cipher/cipher.c.s
.PHONY : cipher/cipher.s

# target to generate assembly for a file
cipher/cipher.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/cipher/cipher.c.s
.PHONY : cipher/cipher.c.s

cipher/null_cipher.o: cipher/null_cipher.c.o
.PHONY : cipher/null_cipher.o

# target to build an object file
cipher/null_cipher.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.o
.PHONY : cipher/null_cipher.c.o

cipher/null_cipher.i: cipher/null_cipher.c.i
.PHONY : cipher/null_cipher.i

# target to preprocess a source file
cipher/null_cipher.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.i
.PHONY : cipher/null_cipher.c.i

cipher/null_cipher.s: cipher/null_cipher.c.s
.PHONY : cipher/null_cipher.s

# target to generate assembly for a file
cipher/null_cipher.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.s
.PHONY : cipher/null_cipher.c.s

debug_aes_cbc.o: debug_aes_cbc.c.o
.PHONY : debug_aes_cbc.o

# target to build an object file
debug_aes_cbc.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_aes_cbc.dir/build.make CMakeFiles/debug_aes_cbc.dir/debug_aes_cbc.c.o
.PHONY : debug_aes_cbc.c.o

debug_aes_cbc.i: debug_aes_cbc.c.i
.PHONY : debug_aes_cbc.i

# target to preprocess a source file
debug_aes_cbc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_aes_cbc.dir/build.make CMakeFiles/debug_aes_cbc.dir/debug_aes_cbc.c.i
.PHONY : debug_aes_cbc.c.i

debug_aes_cbc.s: debug_aes_cbc.c.s
.PHONY : debug_aes_cbc.s

# target to generate assembly for a file
debug_aes_cbc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_aes_cbc.dir/build.make CMakeFiles/debug_aes_cbc.dir/debug_aes_cbc.c.s
.PHONY : debug_aes_cbc.c.s

hash/auth.o: hash/auth.c.o
.PHONY : hash/auth.o

# target to build an object file
hash/auth.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/hash/auth.c.o
.PHONY : hash/auth.c.o

hash/auth.i: hash/auth.c.i
.PHONY : hash/auth.i

# target to preprocess a source file
hash/auth.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/hash/auth.c.i
.PHONY : hash/auth.c.i

hash/auth.s: hash/auth.c.s
.PHONY : hash/auth.s

# target to generate assembly for a file
hash/auth.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/hash/auth.c.s
.PHONY : hash/auth.c.s

hash/hmac.o: hash/hmac.c.o
.PHONY : hash/hmac.o

# target to build an object file
hash/hmac.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/hash/hmac.c.o
.PHONY : hash/hmac.c.o

hash/hmac.i: hash/hmac.c.i
.PHONY : hash/hmac.i

# target to preprocess a source file
hash/hmac.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/hash/hmac.c.i
.PHONY : hash/hmac.c.i

hash/hmac.s: hash/hmac.c.s
.PHONY : hash/hmac.s

# target to generate assembly for a file
hash/hmac.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/hash/hmac.c.s
.PHONY : hash/hmac.c.s

hash/null_auth.o: hash/null_auth.c.o
.PHONY : hash/null_auth.o

# target to build an object file
hash/null_auth.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/hash/null_auth.c.o
.PHONY : hash/null_auth.c.o

hash/null_auth.i: hash/null_auth.c.i
.PHONY : hash/null_auth.i

# target to preprocess a source file
hash/null_auth.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/hash/null_auth.c.i
.PHONY : hash/null_auth.c.i

hash/null_auth.s: hash/null_auth.c.s
.PHONY : hash/null_auth.s

# target to generate assembly for a file
hash/null_auth.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/hash/null_auth.c.s
.PHONY : hash/null_auth.c.s

hash/sha1.o: hash/sha1.c.o
.PHONY : hash/sha1.o

# target to build an object file
hash/sha1.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/hash/sha1.c.o
.PHONY : hash/sha1.c.o

hash/sha1.i: hash/sha1.c.i
.PHONY : hash/sha1.i

# target to preprocess a source file
hash/sha1.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/hash/sha1.c.i
.PHONY : hash/sha1.c.i

hash/sha1.s: hash/sha1.c.s
.PHONY : hash/sha1.s

# target to generate assembly for a file
hash/sha1.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/hash/sha1.c.s
.PHONY : hash/sha1.c.s

kernel/alloc.o: kernel/alloc.c.o
.PHONY : kernel/alloc.o

# target to build an object file
kernel/alloc.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/kernel/alloc.c.o
.PHONY : kernel/alloc.c.o

kernel/alloc.i: kernel/alloc.c.i
.PHONY : kernel/alloc.i

# target to preprocess a source file
kernel/alloc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/kernel/alloc.c.i
.PHONY : kernel/alloc.c.i

kernel/alloc.s: kernel/alloc.c.s
.PHONY : kernel/alloc.s

# target to generate assembly for a file
kernel/alloc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/kernel/alloc.c.s
.PHONY : kernel/alloc.c.s

kernel/crypto_kernel.o: kernel/crypto_kernel.c.o
.PHONY : kernel/crypto_kernel.o

# target to build an object file
kernel/crypto_kernel.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.o
.PHONY : kernel/crypto_kernel.c.o

kernel/crypto_kernel.i: kernel/crypto_kernel.c.i
.PHONY : kernel/crypto_kernel.i

# target to preprocess a source file
kernel/crypto_kernel.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.i
.PHONY : kernel/crypto_kernel.c.i

kernel/crypto_kernel.s: kernel/crypto_kernel.c.s
.PHONY : kernel/crypto_kernel.s

# target to generate assembly for a file
kernel/crypto_kernel.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.s
.PHONY : kernel/crypto_kernel.c.s

kernel/err.o: kernel/err.c.o
.PHONY : kernel/err.o

# target to build an object file
kernel/err.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/kernel/err.c.o
.PHONY : kernel/err.c.o

kernel/err.i: kernel/err.c.i
.PHONY : kernel/err.i

# target to preprocess a source file
kernel/err.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/kernel/err.c.i
.PHONY : kernel/err.c.i

kernel/err.s: kernel/err.c.s
.PHONY : kernel/err.s

# target to generate assembly for a file
kernel/err.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/kernel/err.c.s
.PHONY : kernel/err.c.s

kernel/key.o: kernel/key.c.o
.PHONY : kernel/key.o

# target to build an object file
kernel/key.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/kernel/key.c.o
.PHONY : kernel/key.c.o

kernel/key.i: kernel/key.c.i
.PHONY : kernel/key.i

# target to preprocess a source file
kernel/key.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/kernel/key.c.i
.PHONY : kernel/key.c.i

kernel/key.s: kernel/key.c.s
.PHONY : kernel/key.s

# target to generate assembly for a file
kernel/key.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/kernel/key.c.s
.PHONY : kernel/key.c.s

math/datatypes.o: math/datatypes.c.o
.PHONY : math/datatypes.o

# target to build an object file
math/datatypes.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/math/datatypes.c.o
.PHONY : math/datatypes.c.o

math/datatypes.i: math/datatypes.c.i
.PHONY : math/datatypes.i

# target to preprocess a source file
math/datatypes.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/math/datatypes.c.i
.PHONY : math/datatypes.c.i

math/datatypes.s: math/datatypes.c.s
.PHONY : math/datatypes.s

# target to generate assembly for a file
math/datatypes.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/math/datatypes.c.s
.PHONY : math/datatypes.c.s

math/stat.o: math/stat.c.o
.PHONY : math/stat.o

# target to build an object file
math/stat.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/math/stat.c.o
.PHONY : math/stat.c.o

math/stat.i: math/stat.c.i
.PHONY : math/stat.i

# target to preprocess a source file
math/stat.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/math/stat.c.i
.PHONY : math/stat.c.i

math/stat.s: math/stat.c.s
.PHONY : math/stat.s

# target to generate assembly for a file
math/stat.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/math/stat.c.s
.PHONY : math/stat.c.s

rng/ctr_prng.o: rng/ctr_prng.c.o
.PHONY : rng/ctr_prng.o

# target to build an object file
rng/ctr_prng.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.o
.PHONY : rng/ctr_prng.c.o

rng/ctr_prng.i: rng/ctr_prng.c.i
.PHONY : rng/ctr_prng.i

# target to preprocess a source file
rng/ctr_prng.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.i
.PHONY : rng/ctr_prng.c.i

rng/ctr_prng.s: rng/ctr_prng.c.s
.PHONY : rng/ctr_prng.s

# target to generate assembly for a file
rng/ctr_prng.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.s
.PHONY : rng/ctr_prng.c.s

rng/prng.o: rng/prng.c.o
.PHONY : rng/prng.o

# target to build an object file
rng/prng.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/rng/prng.c.o
.PHONY : rng/prng.c.o

rng/prng.i: rng/prng.c.i
.PHONY : rng/prng.i

# target to preprocess a source file
rng/prng.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/rng/prng.c.i
.PHONY : rng/prng.c.i

rng/prng.s: rng/prng.c.s
.PHONY : rng/prng.s

# target to generate assembly for a file
rng/prng.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/rng/prng.c.s
.PHONY : rng/prng.c.s

rng/rand_source.o: rng/rand_source.c.o
.PHONY : rng/rand_source.o

# target to build an object file
rng/rand_source.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/rng/rand_source.c.o
.PHONY : rng/rand_source.c.o

rng/rand_source.i: rng/rand_source.c.i
.PHONY : rng/rand_source.i

# target to preprocess a source file
rng/rand_source.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/rng/rand_source.c.i
.PHONY : rng/rand_source.c.i

rng/rand_source.s: rng/rand_source.c.s
.PHONY : rng/rand_source.s

# target to generate assembly for a file
rng/rand_source.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/rng/rand_source.c.s
.PHONY : rng/rand_source.c.s

simple_debug.o: simple_debug.c.o
.PHONY : simple_debug.o

# target to build an object file
simple_debug.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_debug.dir/build.make CMakeFiles/simple_debug.dir/simple_debug.c.o
.PHONY : simple_debug.c.o

simple_debug.i: simple_debug.c.i
.PHONY : simple_debug.i

# target to preprocess a source file
simple_debug.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_debug.dir/build.make CMakeFiles/simple_debug.dir/simple_debug.c.i
.PHONY : simple_debug.c.i

simple_debug.s: simple_debug.c.s
.PHONY : simple_debug.s

# target to generate assembly for a file
simple_debug.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_debug.dir/build.make CMakeFiles/simple_debug.dir/simple_debug.c.s
.PHONY : simple_debug.c.s

test/aes_calc.o: test/aes_calc.c.o
.PHONY : test/aes_calc.o

# target to build an object file
test/aes_calc.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_calc.dir/build.make CMakeFiles/aes_calc.dir/test/aes_calc.c.o
.PHONY : test/aes_calc.c.o

test/aes_calc.i: test/aes_calc.c.i
.PHONY : test/aes_calc.i

# target to preprocess a source file
test/aes_calc.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_calc.dir/build.make CMakeFiles/aes_calc.dir/test/aes_calc.c.i
.PHONY : test/aes_calc.c.i

test/aes_calc.s: test/aes_calc.c.s
.PHONY : test/aes_calc.s

# target to generate assembly for a file
test/aes_calc.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_calc.dir/build.make CMakeFiles/aes_calc.dir/test/aes_calc.c.s
.PHONY : test/aes_calc.c.s

test/aes_cbc_interactive.o: test/aes_cbc_interactive.c.o
.PHONY : test/aes_cbc_interactive.o

# target to build an object file
test/aes_cbc_interactive.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_interactive.dir/build.make CMakeFiles/aes_cbc_interactive.dir/test/aes_cbc_interactive.c.o
.PHONY : test/aes_cbc_interactive.c.o

test/aes_cbc_interactive.i: test/aes_cbc_interactive.c.i
.PHONY : test/aes_cbc_interactive.i

# target to preprocess a source file
test/aes_cbc_interactive.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_interactive.dir/build.make CMakeFiles/aes_cbc_interactive.dir/test/aes_cbc_interactive.c.i
.PHONY : test/aes_cbc_interactive.c.i

test/aes_cbc_interactive.s: test/aes_cbc_interactive.c.s
.PHONY : test/aes_cbc_interactive.s

# target to generate assembly for a file
test/aes_cbc_interactive.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_interactive.dir/build.make CMakeFiles/aes_cbc_interactive.dir/test/aes_cbc_interactive.c.s
.PHONY : test/aes_cbc_interactive.c.s

test/aes_cbc_test.o: test/aes_cbc_test.c.o
.PHONY : test/aes_cbc_test.o

# target to build an object file
test/aes_cbc_test.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_test.dir/build.make CMakeFiles/aes_cbc_test.dir/test/aes_cbc_test.c.o
.PHONY : test/aes_cbc_test.c.o

test/aes_cbc_test.i: test/aes_cbc_test.c.i
.PHONY : test/aes_cbc_test.i

# target to preprocess a source file
test/aes_cbc_test.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_test.dir/build.make CMakeFiles/aes_cbc_test.dir/test/aes_cbc_test.c.i
.PHONY : test/aes_cbc_test.c.i

test/aes_cbc_test.s: test/aes_cbc_test.c.s
.PHONY : test/aes_cbc_test.s

# target to generate assembly for a file
test/aes_cbc_test.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_test.dir/build.make CMakeFiles/aes_cbc_test.dir/test/aes_cbc_test.c.s
.PHONY : test/aes_cbc_test.c.s

test/cipher_driver.o: test/cipher_driver.c.o
.PHONY : test/cipher_driver.o

# target to build an object file
test/cipher_driver.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cipher_driver.dir/build.make CMakeFiles/cipher_driver.dir/test/cipher_driver.c.o
.PHONY : test/cipher_driver.c.o

test/cipher_driver.i: test/cipher_driver.c.i
.PHONY : test/cipher_driver.i

# target to preprocess a source file
test/cipher_driver.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cipher_driver.dir/build.make CMakeFiles/cipher_driver.dir/test/cipher_driver.c.i
.PHONY : test/cipher_driver.c.i

test/cipher_driver.s: test/cipher_driver.c.s
.PHONY : test/cipher_driver.s

# target to generate assembly for a file
test/cipher_driver.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cipher_driver.dir/build.make CMakeFiles/cipher_driver.dir/test/cipher_driver.c.s
.PHONY : test/cipher_driver.c.s

test/datatypes_driver.o: test/datatypes_driver.c.o
.PHONY : test/datatypes_driver.o

# target to build an object file
test/datatypes_driver.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/datatypes_driver.dir/build.make CMakeFiles/datatypes_driver.dir/test/datatypes_driver.c.o
.PHONY : test/datatypes_driver.c.o

test/datatypes_driver.i: test/datatypes_driver.c.i
.PHONY : test/datatypes_driver.i

# target to preprocess a source file
test/datatypes_driver.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/datatypes_driver.dir/build.make CMakeFiles/datatypes_driver.dir/test/datatypes_driver.c.i
.PHONY : test/datatypes_driver.c.i

test/datatypes_driver.s: test/datatypes_driver.c.s
.PHONY : test/datatypes_driver.s

# target to generate assembly for a file
test/datatypes_driver.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/datatypes_driver.dir/build.make CMakeFiles/datatypes_driver.dir/test/datatypes_driver.c.s
.PHONY : test/datatypes_driver.c.s

test/env.o: test/env.c.o
.PHONY : test/env.o

# target to build an object file
test/env.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/env.dir/build.make CMakeFiles/env.dir/test/env.c.o
.PHONY : test/env.c.o

test/env.i: test/env.c.i
.PHONY : test/env.i

# target to preprocess a source file
test/env.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/env.dir/build.make CMakeFiles/env.dir/test/env.c.i
.PHONY : test/env.c.i

test/env.s: test/env.c.s
.PHONY : test/env.s

# target to generate assembly for a file
test/env.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/env.dir/build.make CMakeFiles/env.dir/test/env.c.s
.PHONY : test/env.c.s

test/kernel_driver.o: test/kernel_driver.c.o
.PHONY : test/kernel_driver.o

# target to build an object file
test/kernel_driver.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kernel_driver.dir/build.make CMakeFiles/kernel_driver.dir/test/kernel_driver.c.o
.PHONY : test/kernel_driver.c.o

test/kernel_driver.i: test/kernel_driver.c.i
.PHONY : test/kernel_driver.i

# target to preprocess a source file
test/kernel_driver.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kernel_driver.dir/build.make CMakeFiles/kernel_driver.dir/test/kernel_driver.c.i
.PHONY : test/kernel_driver.c.i

test/kernel_driver.s: test/kernel_driver.c.s
.PHONY : test/kernel_driver.s

# target to generate assembly for a file
test/kernel_driver.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kernel_driver.dir/build.make CMakeFiles/kernel_driver.dir/test/kernel_driver.c.s
.PHONY : test/kernel_driver.c.s

test/rand_gen.o: test/rand_gen.c.o
.PHONY : test/rand_gen.o

# target to build an object file
test/rand_gen.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rand_gen.dir/build.make CMakeFiles/rand_gen.dir/test/rand_gen.c.o
.PHONY : test/rand_gen.c.o

test/rand_gen.i: test/rand_gen.c.i
.PHONY : test/rand_gen.i

# target to preprocess a source file
test/rand_gen.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rand_gen.dir/build.make CMakeFiles/rand_gen.dir/test/rand_gen.c.i
.PHONY : test/rand_gen.c.i

test/rand_gen.s: test/rand_gen.c.s
.PHONY : test/rand_gen.s

# target to generate assembly for a file
test/rand_gen.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rand_gen.dir/build.make CMakeFiles/rand_gen.dir/test/rand_gen.c.s
.PHONY : test/rand_gen.c.s

test/sha1_driver.o: test/sha1_driver.c.o
.PHONY : test/sha1_driver.o

# target to build an object file
test/sha1_driver.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sha1_driver.dir/build.make CMakeFiles/sha1_driver.dir/test/sha1_driver.c.o
.PHONY : test/sha1_driver.c.o

test/sha1_driver.i: test/sha1_driver.c.i
.PHONY : test/sha1_driver.i

# target to preprocess a source file
test/sha1_driver.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sha1_driver.dir/build.make CMakeFiles/sha1_driver.dir/test/sha1_driver.c.i
.PHONY : test/sha1_driver.c.i

test/sha1_driver.s: test/sha1_driver.c.s
.PHONY : test/sha1_driver.s

# target to generate assembly for a file
test/sha1_driver.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sha1_driver.dir/build.make CMakeFiles/sha1_driver.dir/test/sha1_driver.c.s
.PHONY : test/sha1_driver.c.s

test/stat_driver.o: test/stat_driver.c.o
.PHONY : test/stat_driver.o

# target to build an object file
test/stat_driver.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stat_driver.dir/build.make CMakeFiles/stat_driver.dir/test/stat_driver.c.o
.PHONY : test/stat_driver.c.o

test/stat_driver.i: test/stat_driver.c.i
.PHONY : test/stat_driver.i

# target to preprocess a source file
test/stat_driver.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stat_driver.dir/build.make CMakeFiles/stat_driver.dir/test/stat_driver.c.i
.PHONY : test/stat_driver.c.i

test/stat_driver.s: test/stat_driver.c.s
.PHONY : test/stat_driver.s

# target to generate assembly for a file
test/stat_driver.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stat_driver.dir/build.make CMakeFiles/stat_driver.dir/test/stat_driver.c.s
.PHONY : test/stat_driver.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... runtest"
	@echo "... aes_calc"
	@echo "... aes_cbc_interactive"
	@echo "... aes_cbc_test"
	@echo "... cipher_driver"
	@echo "... cryptomodule"
	@echo "... datatypes_driver"
	@echo "... debug_aes_cbc"
	@echo "... env"
	@echo "... kernel_driver"
	@echo "... rand_gen"
	@echo "... sha1_driver"
	@echo "... simple_debug"
	@echo "... stat_driver"
	@echo "... ae_xfm/xfm.o"
	@echo "... ae_xfm/xfm.i"
	@echo "... ae_xfm/xfm.s"
	@echo "... cipher/aes.o"
	@echo "... cipher/aes.i"
	@echo "... cipher/aes.s"
	@echo "... cipher/aes_cbc.o"
	@echo "... cipher/aes_cbc.i"
	@echo "... cipher/aes_cbc.s"
	@echo "... cipher/aes_icm.o"
	@echo "... cipher/aes_icm.i"
	@echo "... cipher/aes_icm.s"
	@echo "... cipher/cipher.o"
	@echo "... cipher/cipher.i"
	@echo "... cipher/cipher.s"
	@echo "... cipher/null_cipher.o"
	@echo "... cipher/null_cipher.i"
	@echo "... cipher/null_cipher.s"
	@echo "... debug_aes_cbc.o"
	@echo "... debug_aes_cbc.i"
	@echo "... debug_aes_cbc.s"
	@echo "... hash/auth.o"
	@echo "... hash/auth.i"
	@echo "... hash/auth.s"
	@echo "... hash/hmac.o"
	@echo "... hash/hmac.i"
	@echo "... hash/hmac.s"
	@echo "... hash/null_auth.o"
	@echo "... hash/null_auth.i"
	@echo "... hash/null_auth.s"
	@echo "... hash/sha1.o"
	@echo "... hash/sha1.i"
	@echo "... hash/sha1.s"
	@echo "... kernel/alloc.o"
	@echo "... kernel/alloc.i"
	@echo "... kernel/alloc.s"
	@echo "... kernel/crypto_kernel.o"
	@echo "... kernel/crypto_kernel.i"
	@echo "... kernel/crypto_kernel.s"
	@echo "... kernel/err.o"
	@echo "... kernel/err.i"
	@echo "... kernel/err.s"
	@echo "... kernel/key.o"
	@echo "... kernel/key.i"
	@echo "... kernel/key.s"
	@echo "... math/datatypes.o"
	@echo "... math/datatypes.i"
	@echo "... math/datatypes.s"
	@echo "... math/stat.o"
	@echo "... math/stat.i"
	@echo "... math/stat.s"
	@echo "... rng/ctr_prng.o"
	@echo "... rng/ctr_prng.i"
	@echo "... rng/ctr_prng.s"
	@echo "... rng/prng.o"
	@echo "... rng/prng.i"
	@echo "... rng/prng.s"
	@echo "... rng/rand_source.o"
	@echo "... rng/rand_source.i"
	@echo "... rng/rand_source.s"
	@echo "... simple_debug.o"
	@echo "... simple_debug.i"
	@echo "... simple_debug.s"
	@echo "... test/aes_calc.o"
	@echo "... test/aes_calc.i"
	@echo "... test/aes_calc.s"
	@echo "... test/aes_cbc_interactive.o"
	@echo "... test/aes_cbc_interactive.i"
	@echo "... test/aes_cbc_interactive.s"
	@echo "... test/aes_cbc_test.o"
	@echo "... test/aes_cbc_test.i"
	@echo "... test/aes_cbc_test.s"
	@echo "... test/cipher_driver.o"
	@echo "... test/cipher_driver.i"
	@echo "... test/cipher_driver.s"
	@echo "... test/datatypes_driver.o"
	@echo "... test/datatypes_driver.i"
	@echo "... test/datatypes_driver.s"
	@echo "... test/env.o"
	@echo "... test/env.i"
	@echo "... test/env.s"
	@echo "... test/kernel_driver.o"
	@echo "... test/kernel_driver.i"
	@echo "... test/kernel_driver.s"
	@echo "... test/rand_gen.o"
	@echo "... test/rand_gen.i"
	@echo "... test/rand_gen.s"
	@echo "... test/sha1_driver.o"
	@echo "... test/sha1_driver.i"
	@echo "... test/sha1_driver.s"
	@echo "... test/stat_driver.o"
	@echo "... test/stat_driver.i"
	@echo "... test/stat_driver.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

