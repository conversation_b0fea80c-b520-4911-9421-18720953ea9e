# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build

# Include any dependencies generated for this target.
include CMakeFiles/rand_gen.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/rand_gen.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/rand_gen.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/rand_gen.dir/flags.make

CMakeFiles/rand_gen.dir/codegen:
.PHONY : CMakeFiles/rand_gen.dir/codegen

CMakeFiles/rand_gen.dir/test/rand_gen.c.o: CMakeFiles/rand_gen.dir/flags.make
CMakeFiles/rand_gen.dir/test/rand_gen.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/test/rand_gen.c
CMakeFiles/rand_gen.dir/test/rand_gen.c.o: CMakeFiles/rand_gen.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/rand_gen.dir/test/rand_gen.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/rand_gen.dir/test/rand_gen.c.o -MF CMakeFiles/rand_gen.dir/test/rand_gen.c.o.d -o CMakeFiles/rand_gen.dir/test/rand_gen.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/test/rand_gen.c

CMakeFiles/rand_gen.dir/test/rand_gen.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/rand_gen.dir/test/rand_gen.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/test/rand_gen.c > CMakeFiles/rand_gen.dir/test/rand_gen.c.i

CMakeFiles/rand_gen.dir/test/rand_gen.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/rand_gen.dir/test/rand_gen.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/test/rand_gen.c -o CMakeFiles/rand_gen.dir/test/rand_gen.c.s

# Object files for target rand_gen
rand_gen_OBJECTS = \
"CMakeFiles/rand_gen.dir/test/rand_gen.c.o"

# External object files for target rand_gen
rand_gen_EXTERNAL_OBJECTS =

rand_gen: CMakeFiles/rand_gen.dir/test/rand_gen.c.o
rand_gen: CMakeFiles/rand_gen.dir/build.make
rand_gen: libcryptomodule.a
rand_gen: CMakeFiles/rand_gen.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable rand_gen"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/rand_gen.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/rand_gen.dir/build: rand_gen
.PHONY : CMakeFiles/rand_gen.dir/build

CMakeFiles/rand_gen.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/rand_gen.dir/cmake_clean.cmake
.PHONY : CMakeFiles/rand_gen.dir/clean

CMakeFiles/rand_gen.dir/depend:
	cd /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Downloads/srtp-ics-mr1-crypto /Users/<USER>/Downloads/srtp-ics-mr1-crypto /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles/rand_gen.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/rand_gen.dir/depend

