# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build

# Include any dependencies generated for this target.
include CMakeFiles/stat_driver.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/stat_driver.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/stat_driver.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/stat_driver.dir/flags.make

CMakeFiles/stat_driver.dir/codegen:
.PHONY : CMakeFiles/stat_driver.dir/codegen

CMakeFiles/stat_driver.dir/test/stat_driver.c.o: CMakeFiles/stat_driver.dir/flags.make
CMakeFiles/stat_driver.dir/test/stat_driver.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/test/stat_driver.c
CMakeFiles/stat_driver.dir/test/stat_driver.c.o: CMakeFiles/stat_driver.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/stat_driver.dir/test/stat_driver.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/stat_driver.dir/test/stat_driver.c.o -MF CMakeFiles/stat_driver.dir/test/stat_driver.c.o.d -o CMakeFiles/stat_driver.dir/test/stat_driver.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/test/stat_driver.c

CMakeFiles/stat_driver.dir/test/stat_driver.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/stat_driver.dir/test/stat_driver.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/test/stat_driver.c > CMakeFiles/stat_driver.dir/test/stat_driver.c.i

CMakeFiles/stat_driver.dir/test/stat_driver.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/stat_driver.dir/test/stat_driver.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/test/stat_driver.c -o CMakeFiles/stat_driver.dir/test/stat_driver.c.s

# Object files for target stat_driver
stat_driver_OBJECTS = \
"CMakeFiles/stat_driver.dir/test/stat_driver.c.o"

# External object files for target stat_driver
stat_driver_EXTERNAL_OBJECTS =

stat_driver: CMakeFiles/stat_driver.dir/test/stat_driver.c.o
stat_driver: CMakeFiles/stat_driver.dir/build.make
stat_driver: libcryptomodule.a
stat_driver: CMakeFiles/stat_driver.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable stat_driver"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/stat_driver.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/stat_driver.dir/build: stat_driver
.PHONY : CMakeFiles/stat_driver.dir/build

CMakeFiles/stat_driver.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/stat_driver.dir/cmake_clean.cmake
.PHONY : CMakeFiles/stat_driver.dir/clean

CMakeFiles/stat_driver.dir/depend:
	cd /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Downloads/srtp-ics-mr1-crypto /Users/<USER>/Downloads/srtp-ics-mr1-crypto /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles/stat_driver.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/stat_driver.dir/depend

