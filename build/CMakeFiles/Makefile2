# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/cryptomodule.dir/all
all: CMakeFiles/aes_calc.dir/all
all: CMakeFiles/cipher_driver.dir/all
all: CMakeFiles/datatypes_driver.dir/all
all: CMakeFiles/stat_driver.dir/all
all: CMakeFiles/sha1_driver.dir/all
all: CMakeFiles/kernel_driver.dir/all
all: CMakeFiles/rand_gen.dir/all
all: CMakeFiles/env.dir/all
all: CMakeFiles/aes_cbc_test.dir/all
all: CMakeFiles/aes_cbc_interactive.dir/all
all: CMakeFiles/debug_aes_cbc.dir/all
all: CMakeFiles/simple_debug.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/cryptomodule.dir/codegen
codegen: CMakeFiles/aes_calc.dir/codegen
codegen: CMakeFiles/cipher_driver.dir/codegen
codegen: CMakeFiles/datatypes_driver.dir/codegen
codegen: CMakeFiles/stat_driver.dir/codegen
codegen: CMakeFiles/sha1_driver.dir/codegen
codegen: CMakeFiles/kernel_driver.dir/codegen
codegen: CMakeFiles/rand_gen.dir/codegen
codegen: CMakeFiles/env.dir/codegen
codegen: CMakeFiles/aes_cbc_test.dir/codegen
codegen: CMakeFiles/aes_cbc_interactive.dir/codegen
codegen: CMakeFiles/debug_aes_cbc.dir/codegen
codegen: CMakeFiles/simple_debug.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/cryptomodule.dir/clean
clean: CMakeFiles/aes_calc.dir/clean
clean: CMakeFiles/cipher_driver.dir/clean
clean: CMakeFiles/datatypes_driver.dir/clean
clean: CMakeFiles/stat_driver.dir/clean
clean: CMakeFiles/sha1_driver.dir/clean
clean: CMakeFiles/kernel_driver.dir/clean
clean: CMakeFiles/rand_gen.dir/clean
clean: CMakeFiles/env.dir/clean
clean: CMakeFiles/aes_cbc_test.dir/clean
clean: CMakeFiles/aes_cbc_interactive.dir/clean
clean: CMakeFiles/debug_aes_cbc.dir/clean
clean: CMakeFiles/simple_debug.dir/clean
clean: CMakeFiles/runtest.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/cryptomodule.dir

# All Build rule for target.
CMakeFiles/cryptomodule.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28 "Built target cryptomodule"
.PHONY : CMakeFiles/cryptomodule.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/cryptomodule.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 20
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/cryptomodule.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 0
.PHONY : CMakeFiles/cryptomodule.dir/rule

# Convenience name for target.
cryptomodule: CMakeFiles/cryptomodule.dir/rule
.PHONY : cryptomodule

# codegen rule for target.
CMakeFiles/cryptomodule.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28 "Finished codegen for target cryptomodule"
.PHONY : CMakeFiles/cryptomodule.dir/codegen

# clean rule for target.
CMakeFiles/cryptomodule.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cryptomodule.dir/build.make CMakeFiles/cryptomodule.dir/clean
.PHONY : CMakeFiles/cryptomodule.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/aes_calc.dir

# All Build rule for target.
CMakeFiles/aes_calc.dir/all: CMakeFiles/cryptomodule.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_calc.dir/build.make CMakeFiles/aes_calc.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_calc.dir/build.make CMakeFiles/aes_calc.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=1,2 "Built target aes_calc"
.PHONY : CMakeFiles/aes_calc.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/aes_calc.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/aes_calc.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 0
.PHONY : CMakeFiles/aes_calc.dir/rule

# Convenience name for target.
aes_calc: CMakeFiles/aes_calc.dir/rule
.PHONY : aes_calc

# codegen rule for target.
CMakeFiles/aes_calc.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_calc.dir/build.make CMakeFiles/aes_calc.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=1,2 "Finished codegen for target aes_calc"
.PHONY : CMakeFiles/aes_calc.dir/codegen

# clean rule for target.
CMakeFiles/aes_calc.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_calc.dir/build.make CMakeFiles/aes_calc.dir/clean
.PHONY : CMakeFiles/aes_calc.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/cipher_driver.dir

# All Build rule for target.
CMakeFiles/cipher_driver.dir/all: CMakeFiles/cryptomodule.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cipher_driver.dir/build.make CMakeFiles/cipher_driver.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cipher_driver.dir/build.make CMakeFiles/cipher_driver.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=7,8 "Built target cipher_driver"
.PHONY : CMakeFiles/cipher_driver.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/cipher_driver.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/cipher_driver.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 0
.PHONY : CMakeFiles/cipher_driver.dir/rule

# Convenience name for target.
cipher_driver: CMakeFiles/cipher_driver.dir/rule
.PHONY : cipher_driver

# codegen rule for target.
CMakeFiles/cipher_driver.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cipher_driver.dir/build.make CMakeFiles/cipher_driver.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=7,8 "Finished codegen for target cipher_driver"
.PHONY : CMakeFiles/cipher_driver.dir/codegen

# clean rule for target.
CMakeFiles/cipher_driver.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/cipher_driver.dir/build.make CMakeFiles/cipher_driver.dir/clean
.PHONY : CMakeFiles/cipher_driver.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/datatypes_driver.dir

# All Build rule for target.
CMakeFiles/datatypes_driver.dir/all: CMakeFiles/cryptomodule.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/datatypes_driver.dir/build.make CMakeFiles/datatypes_driver.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/datatypes_driver.dir/build.make CMakeFiles/datatypes_driver.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=29,30 "Built target datatypes_driver"
.PHONY : CMakeFiles/datatypes_driver.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/datatypes_driver.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/datatypes_driver.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 0
.PHONY : CMakeFiles/datatypes_driver.dir/rule

# Convenience name for target.
datatypes_driver: CMakeFiles/datatypes_driver.dir/rule
.PHONY : datatypes_driver

# codegen rule for target.
CMakeFiles/datatypes_driver.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/datatypes_driver.dir/build.make CMakeFiles/datatypes_driver.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=29,30 "Finished codegen for target datatypes_driver"
.PHONY : CMakeFiles/datatypes_driver.dir/codegen

# clean rule for target.
CMakeFiles/datatypes_driver.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/datatypes_driver.dir/build.make CMakeFiles/datatypes_driver.dir/clean
.PHONY : CMakeFiles/datatypes_driver.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/stat_driver.dir

# All Build rule for target.
CMakeFiles/stat_driver.dir/all: CMakeFiles/cryptomodule.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stat_driver.dir/build.make CMakeFiles/stat_driver.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stat_driver.dir/build.make CMakeFiles/stat_driver.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=44,45 "Built target stat_driver"
.PHONY : CMakeFiles/stat_driver.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/stat_driver.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/stat_driver.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 0
.PHONY : CMakeFiles/stat_driver.dir/rule

# Convenience name for target.
stat_driver: CMakeFiles/stat_driver.dir/rule
.PHONY : stat_driver

# codegen rule for target.
CMakeFiles/stat_driver.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stat_driver.dir/build.make CMakeFiles/stat_driver.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=44,45 "Finished codegen for target stat_driver"
.PHONY : CMakeFiles/stat_driver.dir/codegen

# clean rule for target.
CMakeFiles/stat_driver.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/stat_driver.dir/build.make CMakeFiles/stat_driver.dir/clean
.PHONY : CMakeFiles/stat_driver.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/sha1_driver.dir

# All Build rule for target.
CMakeFiles/sha1_driver.dir/all: CMakeFiles/cryptomodule.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sha1_driver.dir/build.make CMakeFiles/sha1_driver.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sha1_driver.dir/build.make CMakeFiles/sha1_driver.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=40,41 "Built target sha1_driver"
.PHONY : CMakeFiles/sha1_driver.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/sha1_driver.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/sha1_driver.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 0
.PHONY : CMakeFiles/sha1_driver.dir/rule

# Convenience name for target.
sha1_driver: CMakeFiles/sha1_driver.dir/rule
.PHONY : sha1_driver

# codegen rule for target.
CMakeFiles/sha1_driver.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sha1_driver.dir/build.make CMakeFiles/sha1_driver.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=40,41 "Finished codegen for target sha1_driver"
.PHONY : CMakeFiles/sha1_driver.dir/codegen

# clean rule for target.
CMakeFiles/sha1_driver.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/sha1_driver.dir/build.make CMakeFiles/sha1_driver.dir/clean
.PHONY : CMakeFiles/sha1_driver.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/kernel_driver.dir

# All Build rule for target.
CMakeFiles/kernel_driver.dir/all: CMakeFiles/cryptomodule.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kernel_driver.dir/build.make CMakeFiles/kernel_driver.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kernel_driver.dir/build.make CMakeFiles/kernel_driver.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=35,36 "Built target kernel_driver"
.PHONY : CMakeFiles/kernel_driver.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/kernel_driver.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/kernel_driver.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 0
.PHONY : CMakeFiles/kernel_driver.dir/rule

# Convenience name for target.
kernel_driver: CMakeFiles/kernel_driver.dir/rule
.PHONY : kernel_driver

# codegen rule for target.
CMakeFiles/kernel_driver.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kernel_driver.dir/build.make CMakeFiles/kernel_driver.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=35,36 "Finished codegen for target kernel_driver"
.PHONY : CMakeFiles/kernel_driver.dir/codegen

# clean rule for target.
CMakeFiles/kernel_driver.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/kernel_driver.dir/build.make CMakeFiles/kernel_driver.dir/clean
.PHONY : CMakeFiles/kernel_driver.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/rand_gen.dir

# All Build rule for target.
CMakeFiles/rand_gen.dir/all: CMakeFiles/cryptomodule.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rand_gen.dir/build.make CMakeFiles/rand_gen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rand_gen.dir/build.make CMakeFiles/rand_gen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=37,38 "Built target rand_gen"
.PHONY : CMakeFiles/rand_gen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/rand_gen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/rand_gen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 0
.PHONY : CMakeFiles/rand_gen.dir/rule

# Convenience name for target.
rand_gen: CMakeFiles/rand_gen.dir/rule
.PHONY : rand_gen

# codegen rule for target.
CMakeFiles/rand_gen.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rand_gen.dir/build.make CMakeFiles/rand_gen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=37,38 "Finished codegen for target rand_gen"
.PHONY : CMakeFiles/rand_gen.dir/codegen

# clean rule for target.
CMakeFiles/rand_gen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/rand_gen.dir/build.make CMakeFiles/rand_gen.dir/clean
.PHONY : CMakeFiles/rand_gen.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/env.dir

# All Build rule for target.
CMakeFiles/env.dir/all: CMakeFiles/cryptomodule.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/env.dir/build.make CMakeFiles/env.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/env.dir/build.make CMakeFiles/env.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=33,34 "Built target env"
.PHONY : CMakeFiles/env.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/env.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/env.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 0
.PHONY : CMakeFiles/env.dir/rule

# Convenience name for target.
env: CMakeFiles/env.dir/rule
.PHONY : env

# codegen rule for target.
CMakeFiles/env.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/env.dir/build.make CMakeFiles/env.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=33,34 "Finished codegen for target env"
.PHONY : CMakeFiles/env.dir/codegen

# clean rule for target.
CMakeFiles/env.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/env.dir/build.make CMakeFiles/env.dir/clean
.PHONY : CMakeFiles/env.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/aes_cbc_test.dir

# All Build rule for target.
CMakeFiles/aes_cbc_test.dir/all: CMakeFiles/cryptomodule.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_test.dir/build.make CMakeFiles/aes_cbc_test.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_test.dir/build.make CMakeFiles/aes_cbc_test.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=5,6 "Built target aes_cbc_test"
.PHONY : CMakeFiles/aes_cbc_test.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/aes_cbc_test.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/aes_cbc_test.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 0
.PHONY : CMakeFiles/aes_cbc_test.dir/rule

# Convenience name for target.
aes_cbc_test: CMakeFiles/aes_cbc_test.dir/rule
.PHONY : aes_cbc_test

# codegen rule for target.
CMakeFiles/aes_cbc_test.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_test.dir/build.make CMakeFiles/aes_cbc_test.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=5,6 "Finished codegen for target aes_cbc_test"
.PHONY : CMakeFiles/aes_cbc_test.dir/codegen

# clean rule for target.
CMakeFiles/aes_cbc_test.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_test.dir/build.make CMakeFiles/aes_cbc_test.dir/clean
.PHONY : CMakeFiles/aes_cbc_test.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/aes_cbc_interactive.dir

# All Build rule for target.
CMakeFiles/aes_cbc_interactive.dir/all: CMakeFiles/cryptomodule.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_interactive.dir/build.make CMakeFiles/aes_cbc_interactive.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_interactive.dir/build.make CMakeFiles/aes_cbc_interactive.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=3,4 "Built target aes_cbc_interactive"
.PHONY : CMakeFiles/aes_cbc_interactive.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/aes_cbc_interactive.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/aes_cbc_interactive.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 0
.PHONY : CMakeFiles/aes_cbc_interactive.dir/rule

# Convenience name for target.
aes_cbc_interactive: CMakeFiles/aes_cbc_interactive.dir/rule
.PHONY : aes_cbc_interactive

# codegen rule for target.
CMakeFiles/aes_cbc_interactive.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_interactive.dir/build.make CMakeFiles/aes_cbc_interactive.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=3,4 "Finished codegen for target aes_cbc_interactive"
.PHONY : CMakeFiles/aes_cbc_interactive.dir/codegen

# clean rule for target.
CMakeFiles/aes_cbc_interactive.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/aes_cbc_interactive.dir/build.make CMakeFiles/aes_cbc_interactive.dir/clean
.PHONY : CMakeFiles/aes_cbc_interactive.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/debug_aes_cbc.dir

# All Build rule for target.
CMakeFiles/debug_aes_cbc.dir/all: CMakeFiles/cryptomodule.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_aes_cbc.dir/build.make CMakeFiles/debug_aes_cbc.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_aes_cbc.dir/build.make CMakeFiles/debug_aes_cbc.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=31,32 "Built target debug_aes_cbc"
.PHONY : CMakeFiles/debug_aes_cbc.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/debug_aes_cbc.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/debug_aes_cbc.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 0
.PHONY : CMakeFiles/debug_aes_cbc.dir/rule

# Convenience name for target.
debug_aes_cbc: CMakeFiles/debug_aes_cbc.dir/rule
.PHONY : debug_aes_cbc

# codegen rule for target.
CMakeFiles/debug_aes_cbc.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_aes_cbc.dir/build.make CMakeFiles/debug_aes_cbc.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=31,32 "Finished codegen for target debug_aes_cbc"
.PHONY : CMakeFiles/debug_aes_cbc.dir/codegen

# clean rule for target.
CMakeFiles/debug_aes_cbc.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/debug_aes_cbc.dir/build.make CMakeFiles/debug_aes_cbc.dir/clean
.PHONY : CMakeFiles/debug_aes_cbc.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/simple_debug.dir

# All Build rule for target.
CMakeFiles/simple_debug.dir/all: CMakeFiles/cryptomodule.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_debug.dir/build.make CMakeFiles/simple_debug.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_debug.dir/build.make CMakeFiles/simple_debug.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=42,43 "Built target simple_debug"
.PHONY : CMakeFiles/simple_debug.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/simple_debug.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 22
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/simple_debug.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 0
.PHONY : CMakeFiles/simple_debug.dir/rule

# Convenience name for target.
simple_debug: CMakeFiles/simple_debug.dir/rule
.PHONY : simple_debug

# codegen rule for target.
CMakeFiles/simple_debug.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_debug.dir/build.make CMakeFiles/simple_debug.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=42,43 "Finished codegen for target simple_debug"
.PHONY : CMakeFiles/simple_debug.dir/codegen

# clean rule for target.
CMakeFiles/simple_debug.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/simple_debug.dir/build.make CMakeFiles/simple_debug.dir/clean
.PHONY : CMakeFiles/simple_debug.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/runtest.dir

# All Build rule for target.
CMakeFiles/runtest.dir/all: CMakeFiles/aes_calc.dir/all
CMakeFiles/runtest.dir/all: CMakeFiles/cipher_driver.dir/all
CMakeFiles/runtest.dir/all: CMakeFiles/datatypes_driver.dir/all
CMakeFiles/runtest.dir/all: CMakeFiles/stat_driver.dir/all
CMakeFiles/runtest.dir/all: CMakeFiles/sha1_driver.dir/all
CMakeFiles/runtest.dir/all: CMakeFiles/kernel_driver.dir/all
CMakeFiles/runtest.dir/all: CMakeFiles/rand_gen.dir/all
CMakeFiles/runtest.dir/all: CMakeFiles/env.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/runtest.dir/build.make CMakeFiles/runtest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/runtest.dir/build.make CMakeFiles/runtest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=39 "Built target runtest"
.PHONY : CMakeFiles/runtest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/runtest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 37
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/runtest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles 0
.PHONY : CMakeFiles/runtest.dir/rule

# Convenience name for target.
runtest: CMakeFiles/runtest.dir/rule
.PHONY : runtest

# codegen rule for target.
CMakeFiles/runtest.dir/codegen: CMakeFiles/aes_calc.dir/all
CMakeFiles/runtest.dir/codegen: CMakeFiles/cipher_driver.dir/all
CMakeFiles/runtest.dir/codegen: CMakeFiles/datatypes_driver.dir/all
CMakeFiles/runtest.dir/codegen: CMakeFiles/stat_driver.dir/all
CMakeFiles/runtest.dir/codegen: CMakeFiles/sha1_driver.dir/all
CMakeFiles/runtest.dir/codegen: CMakeFiles/kernel_driver.dir/all
CMakeFiles/runtest.dir/codegen: CMakeFiles/rand_gen.dir/all
CMakeFiles/runtest.dir/codegen: CMakeFiles/env.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/runtest.dir/build.make CMakeFiles/runtest.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=39 "Finished codegen for target runtest"
.PHONY : CMakeFiles/runtest.dir/codegen

# clean rule for target.
CMakeFiles/runtest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/runtest.dir/build.make CMakeFiles/runtest.dir/clean
.PHONY : CMakeFiles/runtest.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

