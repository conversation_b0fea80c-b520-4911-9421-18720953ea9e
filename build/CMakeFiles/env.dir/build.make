# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build

# Include any dependencies generated for this target.
include CMakeFiles/env.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/env.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/env.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/env.dir/flags.make

CMakeFiles/env.dir/codegen:
.PHONY : CMakeFiles/env.dir/codegen

CMakeFiles/env.dir/test/env.c.o: CMakeFiles/env.dir/flags.make
CMakeFiles/env.dir/test/env.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/test/env.c
CMakeFiles/env.dir/test/env.c.o: CMakeFiles/env.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/env.dir/test/env.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/env.dir/test/env.c.o -MF CMakeFiles/env.dir/test/env.c.o.d -o CMakeFiles/env.dir/test/env.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/test/env.c

CMakeFiles/env.dir/test/env.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/env.dir/test/env.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/test/env.c > CMakeFiles/env.dir/test/env.c.i

CMakeFiles/env.dir/test/env.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/env.dir/test/env.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/test/env.c -o CMakeFiles/env.dir/test/env.c.s

# Object files for target env
env_OBJECTS = \
"CMakeFiles/env.dir/test/env.c.o"

# External object files for target env
env_EXTERNAL_OBJECTS =

env: CMakeFiles/env.dir/test/env.c.o
env: CMakeFiles/env.dir/build.make
env: libcryptomodule.a
env: CMakeFiles/env.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable env"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/env.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/env.dir/build: env
.PHONY : CMakeFiles/env.dir/build

CMakeFiles/env.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/env.dir/cmake_clean.cmake
.PHONY : CMakeFiles/env.dir/clean

CMakeFiles/env.dir/depend:
	cd /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Downloads/srtp-ics-mr1-crypto /Users/<USER>/Downloads/srtp-ics-mr1-crypto /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles/env.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/env.dir/depend

