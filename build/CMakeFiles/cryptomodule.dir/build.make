# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build

# Include any dependencies generated for this target.
include CMakeFiles/cryptomodule.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/cryptomodule.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/cryptomodule.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/cryptomodule.dir/flags.make

CMakeFiles/cryptomodule.dir/codegen:
.PHONY : CMakeFiles/cryptomodule.dir/codegen

CMakeFiles/cryptomodule.dir/cipher/cipher.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/cipher/cipher.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/cipher.c
CMakeFiles/cryptomodule.dir/cipher/cipher.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/cryptomodule.dir/cipher/cipher.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/cipher/cipher.c.o -MF CMakeFiles/cryptomodule.dir/cipher/cipher.c.o.d -o CMakeFiles/cryptomodule.dir/cipher/cipher.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/cipher.c

CMakeFiles/cryptomodule.dir/cipher/cipher.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/cipher/cipher.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/cipher.c > CMakeFiles/cryptomodule.dir/cipher/cipher.c.i

CMakeFiles/cryptomodule.dir/cipher/cipher.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/cipher/cipher.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/cipher.c -o CMakeFiles/cryptomodule.dir/cipher/cipher.c.s

CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/null_cipher.c
CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.o -MF CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.o.d -o CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/null_cipher.c

CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/null_cipher.c > CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.i

CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/null_cipher.c -o CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.s

CMakeFiles/cryptomodule.dir/cipher/aes.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/cipher/aes.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/aes.c
CMakeFiles/cryptomodule.dir/cipher/aes.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/cryptomodule.dir/cipher/aes.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/cipher/aes.c.o -MF CMakeFiles/cryptomodule.dir/cipher/aes.c.o.d -o CMakeFiles/cryptomodule.dir/cipher/aes.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/aes.c

CMakeFiles/cryptomodule.dir/cipher/aes.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/cipher/aes.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/aes.c > CMakeFiles/cryptomodule.dir/cipher/aes.c.i

CMakeFiles/cryptomodule.dir/cipher/aes.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/cipher/aes.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/aes.c -o CMakeFiles/cryptomodule.dir/cipher/aes.c.s

CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/aes_icm.c
CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.o -MF CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.o.d -o CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/aes_icm.c

CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/aes_icm.c > CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.i

CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/aes_icm.c -o CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.s

CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/aes_cbc.c
CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.o -MF CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.o.d -o CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/aes_cbc.c

CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/aes_cbc.c > CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.i

CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/cipher/aes_cbc.c -o CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.s

CMakeFiles/cryptomodule.dir/hash/null_auth.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/hash/null_auth.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/null_auth.c
CMakeFiles/cryptomodule.dir/hash/null_auth.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/cryptomodule.dir/hash/null_auth.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/hash/null_auth.c.o -MF CMakeFiles/cryptomodule.dir/hash/null_auth.c.o.d -o CMakeFiles/cryptomodule.dir/hash/null_auth.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/null_auth.c

CMakeFiles/cryptomodule.dir/hash/null_auth.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/hash/null_auth.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/null_auth.c > CMakeFiles/cryptomodule.dir/hash/null_auth.c.i

CMakeFiles/cryptomodule.dir/hash/null_auth.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/hash/null_auth.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/null_auth.c -o CMakeFiles/cryptomodule.dir/hash/null_auth.c.s

CMakeFiles/cryptomodule.dir/hash/sha1.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/hash/sha1.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/sha1.c
CMakeFiles/cryptomodule.dir/hash/sha1.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object CMakeFiles/cryptomodule.dir/hash/sha1.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/hash/sha1.c.o -MF CMakeFiles/cryptomodule.dir/hash/sha1.c.o.d -o CMakeFiles/cryptomodule.dir/hash/sha1.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/sha1.c

CMakeFiles/cryptomodule.dir/hash/sha1.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/hash/sha1.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/sha1.c > CMakeFiles/cryptomodule.dir/hash/sha1.c.i

CMakeFiles/cryptomodule.dir/hash/sha1.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/hash/sha1.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/sha1.c -o CMakeFiles/cryptomodule.dir/hash/sha1.c.s

CMakeFiles/cryptomodule.dir/hash/hmac.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/hash/hmac.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/hmac.c
CMakeFiles/cryptomodule.dir/hash/hmac.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object CMakeFiles/cryptomodule.dir/hash/hmac.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/hash/hmac.c.o -MF CMakeFiles/cryptomodule.dir/hash/hmac.c.o.d -o CMakeFiles/cryptomodule.dir/hash/hmac.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/hmac.c

CMakeFiles/cryptomodule.dir/hash/hmac.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/hash/hmac.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/hmac.c > CMakeFiles/cryptomodule.dir/hash/hmac.c.i

CMakeFiles/cryptomodule.dir/hash/hmac.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/hash/hmac.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/hmac.c -o CMakeFiles/cryptomodule.dir/hash/hmac.c.s

CMakeFiles/cryptomodule.dir/hash/auth.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/hash/auth.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/auth.c
CMakeFiles/cryptomodule.dir/hash/auth.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object CMakeFiles/cryptomodule.dir/hash/auth.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/hash/auth.c.o -MF CMakeFiles/cryptomodule.dir/hash/auth.c.o.d -o CMakeFiles/cryptomodule.dir/hash/auth.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/auth.c

CMakeFiles/cryptomodule.dir/hash/auth.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/hash/auth.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/auth.c > CMakeFiles/cryptomodule.dir/hash/auth.c.i

CMakeFiles/cryptomodule.dir/hash/auth.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/hash/auth.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/hash/auth.c -o CMakeFiles/cryptomodule.dir/hash/auth.c.s

CMakeFiles/cryptomodule.dir/math/datatypes.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/math/datatypes.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/math/datatypes.c
CMakeFiles/cryptomodule.dir/math/datatypes.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object CMakeFiles/cryptomodule.dir/math/datatypes.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/math/datatypes.c.o -MF CMakeFiles/cryptomodule.dir/math/datatypes.c.o.d -o CMakeFiles/cryptomodule.dir/math/datatypes.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/math/datatypes.c

CMakeFiles/cryptomodule.dir/math/datatypes.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/math/datatypes.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/math/datatypes.c > CMakeFiles/cryptomodule.dir/math/datatypes.c.i

CMakeFiles/cryptomodule.dir/math/datatypes.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/math/datatypes.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/math/datatypes.c -o CMakeFiles/cryptomodule.dir/math/datatypes.c.s

CMakeFiles/cryptomodule.dir/math/stat.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/math/stat.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/math/stat.c
CMakeFiles/cryptomodule.dir/math/stat.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object CMakeFiles/cryptomodule.dir/math/stat.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/math/stat.c.o -MF CMakeFiles/cryptomodule.dir/math/stat.c.o.d -o CMakeFiles/cryptomodule.dir/math/stat.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/math/stat.c

CMakeFiles/cryptomodule.dir/math/stat.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/math/stat.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/math/stat.c > CMakeFiles/cryptomodule.dir/math/stat.c.i

CMakeFiles/cryptomodule.dir/math/stat.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/math/stat.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/math/stat.c -o CMakeFiles/cryptomodule.dir/math/stat.c.s

CMakeFiles/cryptomodule.dir/rng/rand_source.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/rng/rand_source.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/rng/rand_source.c
CMakeFiles/cryptomodule.dir/rng/rand_source.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object CMakeFiles/cryptomodule.dir/rng/rand_source.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/rng/rand_source.c.o -MF CMakeFiles/cryptomodule.dir/rng/rand_source.c.o.d -o CMakeFiles/cryptomodule.dir/rng/rand_source.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/rng/rand_source.c

CMakeFiles/cryptomodule.dir/rng/rand_source.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/rng/rand_source.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/rng/rand_source.c > CMakeFiles/cryptomodule.dir/rng/rand_source.c.i

CMakeFiles/cryptomodule.dir/rng/rand_source.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/rng/rand_source.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/rng/rand_source.c -o CMakeFiles/cryptomodule.dir/rng/rand_source.c.s

CMakeFiles/cryptomodule.dir/rng/prng.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/rng/prng.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/rng/prng.c
CMakeFiles/cryptomodule.dir/rng/prng.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object CMakeFiles/cryptomodule.dir/rng/prng.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/rng/prng.c.o -MF CMakeFiles/cryptomodule.dir/rng/prng.c.o.d -o CMakeFiles/cryptomodule.dir/rng/prng.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/rng/prng.c

CMakeFiles/cryptomodule.dir/rng/prng.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/rng/prng.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/rng/prng.c > CMakeFiles/cryptomodule.dir/rng/prng.c.i

CMakeFiles/cryptomodule.dir/rng/prng.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/rng/prng.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/rng/prng.c -o CMakeFiles/cryptomodule.dir/rng/prng.c.s

CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/rng/ctr_prng.c
CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.o -MF CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.o.d -o CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/rng/ctr_prng.c

CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/rng/ctr_prng.c > CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.i

CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/rng/ctr_prng.c -o CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.s

CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/crypto_kernel.c
CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.o -MF CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.o.d -o CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/crypto_kernel.c

CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/crypto_kernel.c > CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.i

CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/crypto_kernel.c -o CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.s

CMakeFiles/cryptomodule.dir/kernel/alloc.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/kernel/alloc.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/alloc.c
CMakeFiles/cryptomodule.dir/kernel/alloc.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object CMakeFiles/cryptomodule.dir/kernel/alloc.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/kernel/alloc.c.o -MF CMakeFiles/cryptomodule.dir/kernel/alloc.c.o.d -o CMakeFiles/cryptomodule.dir/kernel/alloc.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/alloc.c

CMakeFiles/cryptomodule.dir/kernel/alloc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/kernel/alloc.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/alloc.c > CMakeFiles/cryptomodule.dir/kernel/alloc.c.i

CMakeFiles/cryptomodule.dir/kernel/alloc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/kernel/alloc.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/alloc.c -o CMakeFiles/cryptomodule.dir/kernel/alloc.c.s

CMakeFiles/cryptomodule.dir/kernel/key.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/kernel/key.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/key.c
CMakeFiles/cryptomodule.dir/kernel/key.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object CMakeFiles/cryptomodule.dir/kernel/key.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/kernel/key.c.o -MF CMakeFiles/cryptomodule.dir/kernel/key.c.o.d -o CMakeFiles/cryptomodule.dir/kernel/key.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/key.c

CMakeFiles/cryptomodule.dir/kernel/key.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/kernel/key.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/key.c > CMakeFiles/cryptomodule.dir/kernel/key.c.i

CMakeFiles/cryptomodule.dir/kernel/key.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/kernel/key.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/key.c -o CMakeFiles/cryptomodule.dir/kernel/key.c.s

CMakeFiles/cryptomodule.dir/kernel/err.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/kernel/err.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/err.c
CMakeFiles/cryptomodule.dir/kernel/err.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object CMakeFiles/cryptomodule.dir/kernel/err.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/kernel/err.c.o -MF CMakeFiles/cryptomodule.dir/kernel/err.c.o.d -o CMakeFiles/cryptomodule.dir/kernel/err.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/err.c

CMakeFiles/cryptomodule.dir/kernel/err.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/kernel/err.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/err.c > CMakeFiles/cryptomodule.dir/kernel/err.c.i

CMakeFiles/cryptomodule.dir/kernel/err.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/kernel/err.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/kernel/err.c -o CMakeFiles/cryptomodule.dir/kernel/err.c.s

CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.o: CMakeFiles/cryptomodule.dir/flags.make
CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/ae_xfm/xfm.c
CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.o: CMakeFiles/cryptomodule.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.o -MF CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.o.d -o CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/ae_xfm/xfm.c

CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/ae_xfm/xfm.c > CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.i

CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/ae_xfm/xfm.c -o CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.s

# Object files for target cryptomodule
cryptomodule_OBJECTS = \
"CMakeFiles/cryptomodule.dir/cipher/cipher.c.o" \
"CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.o" \
"CMakeFiles/cryptomodule.dir/cipher/aes.c.o" \
"CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.o" \
"CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.o" \
"CMakeFiles/cryptomodule.dir/hash/null_auth.c.o" \
"CMakeFiles/cryptomodule.dir/hash/sha1.c.o" \
"CMakeFiles/cryptomodule.dir/hash/hmac.c.o" \
"CMakeFiles/cryptomodule.dir/hash/auth.c.o" \
"CMakeFiles/cryptomodule.dir/math/datatypes.c.o" \
"CMakeFiles/cryptomodule.dir/math/stat.c.o" \
"CMakeFiles/cryptomodule.dir/rng/rand_source.c.o" \
"CMakeFiles/cryptomodule.dir/rng/prng.c.o" \
"CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.o" \
"CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.o" \
"CMakeFiles/cryptomodule.dir/kernel/alloc.c.o" \
"CMakeFiles/cryptomodule.dir/kernel/key.c.o" \
"CMakeFiles/cryptomodule.dir/kernel/err.c.o" \
"CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.o"

# External object files for target cryptomodule
cryptomodule_EXTERNAL_OBJECTS =

libcryptomodule.a: CMakeFiles/cryptomodule.dir/cipher/cipher.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/cipher/null_cipher.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/cipher/aes.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/cipher/aes_icm.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/cipher/aes_cbc.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/hash/null_auth.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/hash/sha1.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/hash/hmac.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/hash/auth.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/math/datatypes.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/math/stat.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/rng/rand_source.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/rng/prng.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/rng/ctr_prng.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/kernel/crypto_kernel.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/kernel/alloc.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/kernel/key.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/kernel/err.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/ae_xfm/xfm.c.o
libcryptomodule.a: CMakeFiles/cryptomodule.dir/build.make
libcryptomodule.a: CMakeFiles/cryptomodule.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Linking C static library libcryptomodule.a"
	$(CMAKE_COMMAND) -P CMakeFiles/cryptomodule.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/cryptomodule.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/cryptomodule.dir/build: libcryptomodule.a
.PHONY : CMakeFiles/cryptomodule.dir/build

CMakeFiles/cryptomodule.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/cryptomodule.dir/cmake_clean.cmake
.PHONY : CMakeFiles/cryptomodule.dir/clean

CMakeFiles/cryptomodule.dir/depend:
	cd /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Downloads/srtp-ics-mr1-crypto /Users/<USER>/Downloads/srtp-ics-mr1-crypto /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles/cryptomodule.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/cryptomodule.dir/depend

