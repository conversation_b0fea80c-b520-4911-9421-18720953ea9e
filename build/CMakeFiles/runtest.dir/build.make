# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build

# Utility rule file for runtest.

# Include any custom commands dependencies for this target.
include CMakeFiles/runtest.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/runtest.dir/progress.make

CMakeFiles/runtest: aes_calc
CMakeFiles/runtest: cipher_driver
CMakeFiles/runtest: datatypes_driver
CMakeFiles/runtest: stat_driver
CMakeFiles/runtest: sha1_driver
CMakeFiles/runtest: kernel_driver
CMakeFiles/runtest: rand_gen
CMakeFiles/runtest: env
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Running all tests..."
	./env
	./aes_calc 000102030405060708090a0b0c0d0e0f 00112233445566778899aabbccddeeff
	./cipher_driver -v > /dev/null
	./datatypes_driver -v > /dev/null
	./stat_driver > /dev/null
	./sha1_driver -v > /dev/null
	./kernel_driver -v > /dev/null
	./rand_gen -n 256 > /dev/null

CMakeFiles/runtest.dir/codegen:
.PHONY : CMakeFiles/runtest.dir/codegen

runtest: CMakeFiles/runtest
runtest: CMakeFiles/runtest.dir/build.make
.PHONY : runtest

# Rule to build all files generated by this target.
CMakeFiles/runtest.dir/build: runtest
.PHONY : CMakeFiles/runtest.dir/build

CMakeFiles/runtest.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/runtest.dir/cmake_clean.cmake
.PHONY : CMakeFiles/runtest.dir/clean

CMakeFiles/runtest.dir/depend:
	cd /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Downloads/srtp-ics-mr1-crypto /Users/<USER>/Downloads/srtp-ics-mr1-crypto /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles/runtest.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/runtest.dir/depend

