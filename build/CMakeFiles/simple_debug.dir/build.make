# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /opt/homebrew/bin/cmake

# The command to remove a file.
RM = /opt/homebrew/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build

# Include any dependencies generated for this target.
include CMakeFiles/simple_debug.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/simple_debug.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/simple_debug.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/simple_debug.dir/flags.make

CMakeFiles/simple_debug.dir/codegen:
.PHONY : CMakeFiles/simple_debug.dir/codegen

CMakeFiles/simple_debug.dir/simple_debug.c.o: CMakeFiles/simple_debug.dir/flags.make
CMakeFiles/simple_debug.dir/simple_debug.c.o: /Users/<USER>/Downloads/srtp-ics-mr1-crypto/simple_debug.c
CMakeFiles/simple_debug.dir/simple_debug.c.o: CMakeFiles/simple_debug.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/simple_debug.dir/simple_debug.c.o"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/simple_debug.dir/simple_debug.c.o -MF CMakeFiles/simple_debug.dir/simple_debug.c.o.d -o CMakeFiles/simple_debug.dir/simple_debug.c.o -c /Users/<USER>/Downloads/srtp-ics-mr1-crypto/simple_debug.c

CMakeFiles/simple_debug.dir/simple_debug.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/simple_debug.dir/simple_debug.c.i"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /Users/<USER>/Downloads/srtp-ics-mr1-crypto/simple_debug.c > CMakeFiles/simple_debug.dir/simple_debug.c.i

CMakeFiles/simple_debug.dir/simple_debug.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/simple_debug.dir/simple_debug.c.s"
	/Library/Developer/CommandLineTools/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /Users/<USER>/Downloads/srtp-ics-mr1-crypto/simple_debug.c -o CMakeFiles/simple_debug.dir/simple_debug.c.s

# Object files for target simple_debug
simple_debug_OBJECTS = \
"CMakeFiles/simple_debug.dir/simple_debug.c.o"

# External object files for target simple_debug
simple_debug_EXTERNAL_OBJECTS =

simple_debug: CMakeFiles/simple_debug.dir/simple_debug.c.o
simple_debug: CMakeFiles/simple_debug.dir/build.make
simple_debug: libcryptomodule.a
simple_debug: CMakeFiles/simple_debug.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking C executable simple_debug"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/simple_debug.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/simple_debug.dir/build: simple_debug
.PHONY : CMakeFiles/simple_debug.dir/build

CMakeFiles/simple_debug.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/simple_debug.dir/cmake_clean.cmake
.PHONY : CMakeFiles/simple_debug.dir/clean

CMakeFiles/simple_debug.dir/depend:
	cd /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/Downloads/srtp-ics-mr1-crypto /Users/<USER>/Downloads/srtp-ics-mr1-crypto /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build /Users/<USER>/Downloads/srtp-ics-mr1-crypto/build/CMakeFiles/simple_debug.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/simple_debug.dir/depend

