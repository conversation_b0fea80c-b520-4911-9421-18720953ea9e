cmake_minimum_required(VERSION 3.16)
project(srtp_crypto)

# Set C standard
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Set build type to Debug for better debugging experience
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Debug)
endif()

# Compiler flags
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall")
set(CMAKE_C_FLAGS_DEBUG "-g -O0")
set(CMAKE_C_FLAGS_RELEASE "-O3")

# Add definitions
add_definitions(-DHAVE_CONFIG_H)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Source files for the crypto library
set(CIPHER_SOURCES
    cipher/cipher.c
    cipher/null_cipher.c
    cipher/aes.c
    cipher/aes_icm.c
    cipher/aes_cbc.c
)

set(HASH_SOURCES
    hash/null_auth.c
    hash/sha1.c
    hash/hmac.c
    hash/auth.c
)

set(MATH_SOURCES
    math/datatypes.c
    math/stat.c
)

set(RNG_SOURCES
    rng/rand_source.c
    rng/prng.c
    rng/ctr_prng.c
)

set(KERNEL_SOURCES
    kernel/crypto_kernel.c
    kernel/alloc.c
    kernel/key.c
    kernel/err.c
)

set(XFM_SOURCES
    ae_xfm/xfm.c
)

# Create the crypto library
add_library(cryptomodule STATIC
    ${CIPHER_SOURCES}
    ${HASH_SOURCES}
    ${MATH_SOURCES}
    ${RNG_SOURCES}
    ${KERNEL_SOURCES}
    ${XFM_SOURCES}
)

# Test executables
add_executable(aes_calc test/aes_calc.c)
target_link_libraries(aes_calc cryptomodule)

add_executable(cipher_driver test/cipher_driver.c)
target_link_libraries(cipher_driver cryptomodule)

add_executable(datatypes_driver test/datatypes_driver.c)
target_link_libraries(datatypes_driver cryptomodule)

add_executable(stat_driver test/stat_driver.c)
target_link_libraries(stat_driver cryptomodule)

add_executable(sha1_driver test/sha1_driver.c)
target_link_libraries(sha1_driver cryptomodule)

add_executable(kernel_driver test/kernel_driver.c)
target_link_libraries(kernel_driver cryptomodule)

add_executable(rand_gen test/rand_gen.c)
target_link_libraries(rand_gen cryptomodule)

add_executable(env test/env.c)
target_link_libraries(env cryptomodule)

# Our custom AES CBC test programs
add_executable(aes_cbc_test test/aes_cbc_test.c)
target_link_libraries(aes_cbc_test cryptomodule)

add_executable(aes_cbc_interactive test/aes_cbc_interactive.c)
target_link_libraries(aes_cbc_interactive cryptomodule)

# Debug tools
add_executable(debug_aes_cbc debug_aes_cbc.c)
target_link_libraries(debug_aes_cbc cryptomodule)

add_executable(simple_debug simple_debug.c)
target_link_libraries(simple_debug cryptomodule)

# Set the main executable for easy running
set_property(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR} PROPERTY VS_STARTUP_PROJECT simple_debug)

# Custom target for running tests
add_custom_target(runtest
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/env
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/aes_calc 000102030405060708090a0b0c0d0e0f 00112233445566778899aabbccddeeff
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/cipher_driver -v > /dev/null
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/datatypes_driver -v > /dev/null
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/stat_driver > /dev/null
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/sha1_driver -v > /dev/null
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/kernel_driver -v > /dev/null
    COMMAND ${CMAKE_CURRENT_BINARY_DIR}/rand_gen -n 256 > /dev/null
    DEPENDS aes_calc cipher_driver datatypes_driver stat_driver sha1_driver kernel_driver rand_gen env
    COMMENT "Running all tests..."
)

# Print some helpful information
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C compiler: ${CMAKE_C_COMPILER}")
message(STATUS "C flags: ${CMAKE_C_FLAGS}")

# Installation (optional)
install(TARGETS cryptomodule
    ARCHIVE DESTINATION lib
)

install(DIRECTORY include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h"
)

install(TARGETS 
    aes_calc cipher_driver datatypes_driver stat_driver 
    sha1_driver kernel_driver rand_gen env
    aes_cbc_test aes_cbc_interactive debug_aes_cbc simple_debug
    RUNTIME DESTINATION bin
)
