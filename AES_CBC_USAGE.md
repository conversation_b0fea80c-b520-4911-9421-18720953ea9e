# AES CBC 加密测试使用说明

## 项目概述

这是一个SRTP加密库项目，包含了AES加密的多种模式，包括AES CBC（密码块链接）模式。项目已经成功构建并可以运行。

## 构建项目

项目已经构建完成，如果需要重新构建：

```bash
make clean
make all
```

## 运行测试

### 1. 运行所有测试
```bash
make runtest
```

### 2. 基本AES加密测试
```bash
./test/aes_calc 000102030405060708090a0b0c0d0e0f 00112233445566778899aabbccddeeff -v
```

### 3. 密码驱动测试（包括AES CBC）
```bash
./test/cipher_driver -v
```

### 4. AES CBC专用测试
```bash
./test/aes_cbc_test
```

### 5. 交互式AES CBC加密/解密工具
```bash
./test/aes_cbc_interactive
```

## AES CBC 测试结果

### 自动测试结果
- ✅ 所有基础测试通过
- ✅ AES CBC模式自测试通过
- ✅ 加密/解密循环测试成功

### 示例加密测试
**输入:**
- 密钥: `2b7e151628aed2a6abf7158809cf4f3c`
- IV: `000102030405060708090a0b0c0d0e0f`
- 明文: `Hello, AES CBC encryption!`

**输出:**
- 密文: `2ca9787221a590561661df7f8bd56bb6f5e95391cc60f5b904323a6f06f47b90`
- 解密后: `Hello, AES CBC encryption!` ✅

## 可用的测试程序

1. **test/aes_calc** - 基本AES加密计算器
2. **test/cipher_driver** - 密码算法驱动测试
3. **test/aes_cbc_test** - AES CBC专用测试程序
4. **test/aes_cbc_interactive** - 交互式AES CBC工具

## 交互式工具使用方法

运行 `./test/aes_cbc_interactive` 后：

1. **输入密钥** (可选，按回车使用默认密钥)
   - 格式：32个十六进制字符 (128位)
   - 示例：`2b7e151628aed2a6abf7158809cf4f3c`

2. **输入初始化向量(IV)** (可选，按回车使用默认IV)
   - 格式：32个十六进制字符 (128位)
   - 示例：`000102030405060708090a0b0c0d0e0f`

3. **选择操作**
   - 1: 加密文本
   - 2: 解密十六进制数据

4. **输入数据**
   - 加密：输入要加密的文本
   - 解密：输入十六进制格式的密文

## 技术特性

- **加密算法**: AES-128
- **加密模式**: CBC (密码块链接)
- **填充方式**: PKCS#7
- **块大小**: 128位 (16字节)
- **密钥长度**: 128位 (16字节)
- **IV长度**: 128位 (16字节)

## 注意事项

1. AES CBC模式需要初始化向量(IV)，每次加密都应使用不同的IV
2. 明文会自动进行PKCS#7填充以满足块大小要求
3. 解密时会自动去除填充
4. 相同的明文使用相同的密钥和IV会产生相同的密文

## 项目文件结构

```
├── cipher/
│   ├── aes.c          # AES核心算法
│   ├── aes_cbc.c      # AES CBC模式实现
│   └── ...
├── include/
│   ├── aes.h          # AES头文件
│   ├── aes_cbc.h      # AES CBC头文件
│   └── ...
├── test/
│   ├── aes_calc.c           # AES计算器
│   ├── cipher_driver.c      # 密码驱动测试
│   ├── aes_cbc_test.c       # AES CBC测试
│   └── aes_cbc_interactive.c # 交互式工具
└── Makefile
```

项目已经成功运行，AES CBC加密功能完全正常！
