# AES CBC 调试工具使用指南

## 概述

我为你创建了多个AES CBC调试工具，每个工具都有不同的用途和详细程度。这些工具可以帮助你深入理解AES CBC模式的工作原理。

## 可用的调试工具

### 1. `debug_aes_cbc` - 详细调试工具
**用途**: 深入分析AES CBC的每个步骤
**特点**: 
- 手动实现vs库实现对比
- 逐块分析CBC过程
- 详细的填充分析
- XOR操作可视化

**使用方法**:
```bash
./debug_aes_cbc
```

**输出示例**:
```
=== MANUAL CBC ENCRYPTION DEBUG ===
Input Parameters:
Key: 2b7e1516 28aed2a6 abf71588 09cf4f3c 
IV: 00010203 04050607 08090a0b 0c0d0e0f 
Plaintext: 48656c6c 6f20576f 726c6421 

Padding Analysis:
Original length: 12 bytes
Padding needed: 4 bytes
Padded length: 16 bytes
Number of blocks: 1

CBC Encryption Process:
--- Block 1 ---
Block 1 plaintext: 48656c6c 6f20576f 726c6421 04040404 
Block 0 previous ciphertext: 00010203 04050607 08090a0b 0c0d0e0f 
Block 1 after XOR: 48646e6f 6b255168 7a656e2a 08090a0b 
Block 1 after AES encrypt: 1357e9b9 3b14f0af cea5f7df fc1040b9 
```

### 2. `simple_debug` - 简化调试工具
**用途**: 快速测试和验证
**特点**:
- 简洁的输出格式
- 支持命令行参数
- 交互式模式
- 自动验证功能

**使用方法**:
```bash
# 测试特定消息
./simple_debug "Your message here"

# 默认测试套件 + 交互模式
./simple_debug

# 测试多个消息
./simple_debug "Message 1" "Message 2" "Message 3"
```

**输出示例**:
```
=== AES CBC Test ===
Message: "My test message"
Key            : 2b7e1516 28aed2a6 abf71588 09cf4f3c 
IV             : 00010203 04050607 08090a0b 0c0d0e0f 
Plaintext      : 4d792074 65737420 6d657373 616765

--- Encryption ---
✓ Encryption successful
Original length: 15 bytes
Encrypted length: 16 bytes
Ciphertext     : 5c73e851 e703ea45 7e57af3e 777b24ad 

--- Decryption ---
✓ Decryption successful
Decrypted length: 15 bytes
✓ Verification: Decrypted text matches original!
```

### 3. `test/aes_cbc_test` - 标准测试程序
**用途**: 验证AES CBC实现的正确性
**特点**:
- 使用NIST测试向量
- 完整的加密/解密循环
- 自动验证

**使用方法**:
```bash
./test/aes_cbc_test
```

### 4. `test/aes_cbc_interactive` - 交互式工具
**用途**: 手动测试自定义数据
**特点**:
- 自定义密钥和IV
- 支持十六进制输入
- 文本和二进制数据处理

**使用方法**:
```bash
./test/aes_cbc_interactive
```

## 编译说明

所有工具都已编译完成，如需重新编译：

```bash
# 详细调试工具
gcc -DHAVE_CONFIG_H -Iinclude -I./include -Wall -O0 -g -L. debug_aes_cbc.c -o debug_aes_cbc -lcryptomodule

# 简化调试工具
gcc -DHAVE_CONFIG_H -Iinclude -I./include -Wall -O0 -g -L. simple_debug.c -o simple_debug -lcryptomodule

# 其他测试程序
make all
```

## 调试场景和推荐工具

### 场景1: 理解CBC工作原理
**推荐**: `debug_aes_cbc`
- 查看每个块的处理过程
- 理解XOR操作
- 分析填充机制

### 场景2: 快速验证功能
**推荐**: `simple_debug`
- 测试特定消息
- 验证加密/解密正确性
- 批量测试

### 场景3: 性能测试
**推荐**: `test/cipher_driver -t`
- 吞吐量测试
- 性能基准

### 场景4: 自定义测试
**推荐**: `test/aes_cbc_interactive`
- 使用自己的密钥和IV
- 测试特定的十六进制数据

## 调试技巧

### 1. 修改测试参数
在源代码中修改以下参数来测试不同场景：

```c
// 修改密钥
unsigned char key[16] = {
    0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
    0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
};

// 修改IV
unsigned char iv[16] = {
    0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
    0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f
};
```

### 2. 启用详细调试
在 `debug_aes_cbc.c` 中设置：
```c
#define DEBUG_VERBOSE 1
```

### 3. 测试边界情况
- 空字符串
- 单字符
- 正好16字节的消息
- 很长的消息

## 常见问题排查

### 问题1: 加密结果不一致
**可能原因**: IV重用或密钥不同
**调试方法**: 使用 `simple_debug` 检查密钥和IV

### 问题2: 解密失败
**可能原因**: 密文损坏或填充问题
**调试方法**: 使用 `debug_aes_cbc` 查看详细过程

### 问题3: 长度不匹配
**可能原因**: 填充处理问题
**调试方法**: 检查填充分析输出

## 示例调试会话

```bash
# 1. 快速测试
./simple_debug "Hello World"

# 2. 详细分析
./debug_aes_cbc

# 3. 交互式测试
./test/aes_cbc_interactive

# 4. 性能测试
./test/cipher_driver -v
```

## 技术细节

- **算法**: AES-128
- **模式**: CBC (Cipher Block Chaining)
- **填充**: PKCS#7
- **块大小**: 128位 (16字节)
- **密钥长度**: 128位 (16字节)
- **IV长度**: 128位 (16字节)

这些工具应该能满足你所有的AES CBC调试需求！
