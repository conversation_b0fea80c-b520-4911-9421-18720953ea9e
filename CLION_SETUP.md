# CLion 设置指南

## 问题原因

在CLion中无法直接运行的常见原因：

1. **项目没有CMakeLists.txt文件** - CLion主要使用CMake作为构建系统
2. **库依赖路径问题** - 找不到libcryptomodule.a库
3. **头文件路径问题** - 找不到include目录
4. **运行配置未正确设置** - 没有配置可执行文件

## 解决方案

我已经为你创建了`CMakeLists.txt`文件并修复了所有编译问题，现在按以下步骤在CLion中设置项目：

### 步骤1: 在CLion中打开项目

1. 打开CLion
2. 选择 `File` -> `Open`
3. 选择项目根目录 `/Users/<USER>/Downloads/srtp-ics-mr1-crypto`
4. CLion会自动检测到CMakeLists.txt并开始配置项目

### 步骤2: 等待CMake配置完成

CLion会自动：
- 解析CMakeLists.txt
- 配置构建系统
- 索引源代码
- 创建运行配置

**注意**: 如果CLion提示选择构建目录，选择`build`目录或让CLion自动创建。

### 步骤3: 选择运行目标

在CLion的工具栏中，你会看到一个下拉菜单，可以选择以下运行目标：

- `simple_debug` - 简化调试工具（推荐作为主程序）
- `debug_aes_cbc` - 详细调试工具
- `aes_cbc_test` - AES CBC测试程序
- `aes_cbc_interactive` - 交互式工具
- `aes_calc` - AES计算器
- `cipher_driver` - 密码驱动测试

### 步骤4: 运行程序

1. 选择想要运行的目标（如`simple_debug`）
2. 点击绿色的运行按钮（▶️）或按`Shift+F10`
3. 程序会在CLion的运行窗口中执行

### 步骤5: 调试程序

1. 在代码中设置断点（点击行号左侧）
2. 选择调试目标
3. 点击调试按钮（🐛）或按`Shift+F9`
4. 程序会在断点处暂停，你可以检查变量值

## 运行配置详情

CMakeLists.txt已经配置了以下可执行文件：

### 主要调试工具
- **simple_debug**: 快速AES CBC测试工具
- **debug_aes_cbc**: 详细的CBC过程分析工具

### 测试程序
- **aes_cbc_test**: 标准AES CBC测试
- **aes_cbc_interactive**: 交互式加密/解密工具
- **cipher_driver**: 完整的密码算法测试

### 原始测试工具
- **aes_calc**: AES加密计算器
- **env**: 环境信息显示

## 如果仍然有问题

### 问题1: CMake配置失败
**解决方法**:
1. 确保安装了CMake（版本3.16+）
2. 在CLion中：`File` -> `Settings` -> `Build, Execution, Deployment` -> `CMake`
3. 检查CMake路径是否正确

### 问题2: 编译错误
**解决方法**:
1. 清理构建：`Build` -> `Clean`
2. 重新构建：`Build` -> `Rebuild Project`
3. 检查编译器设置

### 问题3: 找不到头文件
**解决方法**:
1. 确保include目录存在
2. 检查CMakeLists.txt中的include_directories设置
3. 重新加载CMake项目：`Tools` -> `CMake` -> `Reload CMake Project`

### 问题4: 运行时错误
**解决方法**:
1. 检查工作目录设置
2. 在运行配置中设置正确的工作目录
3. 确保所有依赖库都已正确链接

## 手动创建运行配置

如果自动配置不工作，可以手动创建：

1. 点击 `Run` -> `Edit Configurations...`
2. 点击 `+` 添加新配置
3. 选择 `CMake Application`
4. 设置以下参数：
   - **Name**: `Simple Debug`
   - **Target**: `simple_debug`
   - **Executable**: `simple_debug`
   - **Working directory**: `$ProjectFileDir$`

## 推荐的开发工作流

1. **快速测试**: 运行`simple_debug`
2. **详细调试**: 运行`debug_aes_cbc`
3. **交互测试**: 运行`aes_cbc_interactive`
4. **设置断点**: 在关键函数处设置断点进行调试
5. **查看变量**: 使用CLion的调试器查看变量值

## 常用快捷键

- `Shift+F10`: 运行
- `Shift+F9`: 调试
- `Ctrl+F8`: 切换断点
- `F8`: 单步执行
- `F7`: 步入函数
- `Shift+F8`: 步出函数

现在你应该能够在CLion中正常运行和调试AES CBC程序了！
