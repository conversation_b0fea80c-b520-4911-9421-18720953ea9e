/*
 * aes_cbc_test.c
 * 
 * A simple AES CBC encryption/decryption test program
 *
 * This program demonstrates how to use AES CBC mode for encryption and decryption
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "aes_cbc.h"
#include "cipher.h"

// External declaration of AES CBC cipher type
extern cipher_type_t aes_cbc;

void print_hex(const char* label, const unsigned char* data, int len) {
    printf("%s: ", label);
    for (int i = 0; i < len; i++) {
        printf("%02x", data[i]);
    }
    printf("\n");
}

int main() {
    printf("AES CBC Encryption/Decryption Test\n");
    printf("==================================\n\n");

    // Test key (128-bit)
    unsigned char key[16] = {
        0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
        0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
    };

    // Initialization Vector (IV)
    unsigned char iv[16] = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f
    };

    // Test plaintext (32 bytes - 2 blocks)
    unsigned char plaintext[32] = {
        0x6b, 0xc1, 0xbe, 0xe2, 0x2e, 0x40, 0x9f, 0x96,
        0xe9, 0x3d, 0x7e, 0x11, 0x73, 0x93, 0x17, 0x2a,
        0xae, 0x2d, 0x8a, 0x57, 0x1e, 0x03, 0xac, 0x9c,
        0x9e, 0xb7, 0x6f, 0xac, 0x45, 0xaf, 0x8e, 0x51
    };

    // Buffer for ciphertext (larger to accommodate padding)
    unsigned char ciphertext[64];
    unsigned char decrypted[64];

    // Copy plaintext to ciphertext buffer for encryption
    memcpy(ciphertext, plaintext, 32);
    
    printf("Test 1: AES CBC Encryption\n");
    printf("---------------------------\n");
    
    print_hex("Key", key, 16);
    print_hex("IV", iv, 16);
    print_hex("Plaintext", plaintext, 32);

    // Create and initialize cipher for encryption
    cipher_t *cipher_enc;
    err_status_t status;
    
    status = cipher_type_alloc(&aes_cbc, &cipher_enc, 16);
    if (status != err_status_ok) {
        printf("Error: Failed to allocate cipher for encryption\n");
        return 1;
    }

    status = cipher_init(cipher_enc, key, direction_encrypt);
    if (status != err_status_ok) {
        printf("Error: Failed to initialize cipher for encryption\n");
        cipher_dealloc(cipher_enc);
        return 1;
    }

    status = cipher_set_iv(cipher_enc, iv);
    if (status != err_status_ok) {
        printf("Error: Failed to set IV for encryption\n");
        cipher_dealloc(cipher_enc);
        return 1;
    }

    // Encrypt the data
    unsigned int len = 32;
    status = cipher_encrypt(cipher_enc, ciphertext, &len);
    if (status != err_status_ok) {
        printf("Error: Encryption failed\n");
        cipher_dealloc(cipher_enc);
        return 1;
    }

    print_hex("Ciphertext", ciphertext, len);
    printf("Encrypted length: %u bytes\n\n", len);

    cipher_dealloc(cipher_enc);

    printf("Test 2: AES CBC Decryption\n");
    printf("---------------------------\n");
    
    // Copy ciphertext to decrypted buffer for decryption
    memcpy(decrypted, ciphertext, len);

    // Create and initialize cipher for decryption
    cipher_t *cipher_dec;
    
    status = cipher_type_alloc(&aes_cbc, &cipher_dec, 16);
    if (status != err_status_ok) {
        printf("Error: Failed to allocate cipher for decryption\n");
        return 1;
    }

    status = cipher_init(cipher_dec, key, direction_decrypt);
    if (status != err_status_ok) {
        printf("Error: Failed to initialize cipher for decryption\n");
        cipher_dealloc(cipher_dec);
        return 1;
    }

    status = cipher_set_iv(cipher_dec, iv);
    if (status != err_status_ok) {
        printf("Error: Failed to set IV for decryption\n");
        cipher_dealloc(cipher_dec);
        return 1;
    }

    // Decrypt the data
    unsigned int dec_len = len;
    status = cipher_decrypt(cipher_dec, decrypted, &dec_len);
    if (status != err_status_ok) {
        printf("Error: Decryption failed\n");
        cipher_dealloc(cipher_dec);
        return 1;
    }

    print_hex("Decrypted", decrypted, dec_len);
    printf("Decrypted length: %u bytes\n\n", dec_len);

    cipher_dealloc(cipher_dec);

    // Verify that decryption matches original plaintext
    printf("Verification\n");
    printf("------------\n");
    if (memcmp(plaintext, decrypted, 32) == 0) {
        printf("✓ SUCCESS: Decrypted text matches original plaintext!\n");
    } else {
        printf("✗ FAILURE: Decrypted text does not match original plaintext!\n");
        return 1;
    }

    printf("\nAES CBC test completed successfully!\n");
    return 0;
}
