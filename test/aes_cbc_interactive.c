/*
 * aes_cbc_interactive.c
 * 
 * Interactive AES CBC encryption/decryption tool
 *
 * This program allows you to encrypt and decrypt text using AES CBC mode
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "aes_cbc.h"
#include "cipher.h"

// External declaration of AES CBC cipher type
extern cipher_type_t aes_cbc;

void print_hex(const char* label, const unsigned char* data, int len) {
    printf("%s: ", label);
    for (int i = 0; i < len; i++) {
        printf("%02x", data[i]);
    }
    printf("\n");
}

int hex_char_to_int(char c) {
    if (c >= '0' && c <= '9') return c - '0';
    if (c >= 'a' && c <= 'f') return c - 'a' + 10;
    if (c >= 'A' && c <= 'F') return c - 'A' + 10;
    return -1;
}

int hex_string_to_bytes(const char* hex_str, unsigned char* bytes, int max_bytes) {
    int len = strlen(hex_str);
    if (len % 2 != 0) return -1; // Odd length
    
    int byte_count = len / 2;
    if (byte_count > max_bytes) return -1; // Too long
    
    for (int i = 0; i < byte_count; i++) {
        int high = hex_char_to_int(hex_str[i * 2]);
        int low = hex_char_to_int(hex_str[i * 2 + 1]);
        if (high == -1 || low == -1) return -1; // Invalid hex
        bytes[i] = (high << 4) | low;
    }
    
    return byte_count;
}

void string_to_bytes(const char* str, unsigned char* bytes, int* len) {
    *len = strlen(str);
    memcpy(bytes, str, *len);
}

int main() {
    printf("Interactive AES CBC Encryption/Decryption Tool\n");
    printf("==============================================\n\n");

    char input[1024];
    unsigned char key[16];
    unsigned char iv[16];
    unsigned char data[256];
    unsigned char result[512];
    int data_len;
    
    // Get key
    printf("Enter 128-bit key (32 hex characters, or press Enter for default): ");
    fgets(input, sizeof(input), stdin);
    input[strcspn(input, "\n")] = 0; // Remove newline
    
    if (strlen(input) == 0) {
        // Use default key
        unsigned char default_key[16] = {
            0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
            0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
        };
        memcpy(key, default_key, 16);
        printf("Using default key.\n");
    } else {
        if (hex_string_to_bytes(input, key, 16) != 16) {
            printf("Error: Key must be exactly 32 hex characters (128 bits)\n");
            return 1;
        }
    }
    
    // Get IV
    printf("Enter 128-bit IV (32 hex characters, or press Enter for default): ");
    fgets(input, sizeof(input), stdin);
    input[strcspn(input, "\n")] = 0; // Remove newline
    
    if (strlen(input) == 0) {
        // Use default IV
        unsigned char default_iv[16] = {
            0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
            0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f
        };
        memcpy(iv, default_iv, 16);
        printf("Using default IV.\n");
    } else {
        if (hex_string_to_bytes(input, iv, 16) != 16) {
            printf("Error: IV must be exactly 32 hex characters (128 bits)\n");
            return 1;
        }
    }
    
    printf("\n");
    print_hex("Key", key, 16);
    print_hex("IV", iv, 16);
    printf("\n");
    
    // Get operation
    printf("Choose operation:\n");
    printf("1. Encrypt text\n");
    printf("2. Decrypt hex data\n");
    printf("Enter choice (1 or 2): ");
    fgets(input, sizeof(input), stdin);
    
    int choice = atoi(input);
    
    if (choice == 1) {
        // Encryption
        printf("Enter text to encrypt: ");
        fgets(input, sizeof(input), stdin);
        input[strcspn(input, "\n")] = 0; // Remove newline
        
        string_to_bytes(input, data, &data_len);
        memcpy(result, data, data_len);
        
        printf("\nEncryption:\n");
        printf("-----------\n");
        print_hex("Plaintext", data, data_len);
        
        // Create and initialize cipher for encryption
        cipher_t *cipher_enc;
        err_status_t status;
        
        status = cipher_type_alloc(&aes_cbc, &cipher_enc, 16);
        if (status != err_status_ok) {
            printf("Error: Failed to allocate cipher for encryption\n");
            return 1;
        }

        status = cipher_init(cipher_enc, key, direction_encrypt);
        if (status != err_status_ok) {
            printf("Error: Failed to initialize cipher for encryption\n");
            cipher_dealloc(cipher_enc);
            return 1;
        }

        status = cipher_set_iv(cipher_enc, iv);
        if (status != err_status_ok) {
            printf("Error: Failed to set IV for encryption\n");
            cipher_dealloc(cipher_enc);
            return 1;
        }

        // Encrypt the data
        unsigned int len = data_len;
        status = cipher_encrypt(cipher_enc, result, &len);
        if (status != err_status_ok) {
            printf("Error: Encryption failed\n");
            cipher_dealloc(cipher_enc);
            return 1;
        }

        print_hex("Ciphertext", result, len);
        printf("Encrypted length: %u bytes\n", len);
        
        cipher_dealloc(cipher_enc);
        
    } else if (choice == 2) {
        // Decryption
        printf("Enter hex data to decrypt: ");
        fgets(input, sizeof(input), stdin);
        input[strcspn(input, "\n")] = 0; // Remove newline
        
        data_len = hex_string_to_bytes(input, data, sizeof(data));
        if (data_len <= 0) {
            printf("Error: Invalid hex data\n");
            return 1;
        }
        
        memcpy(result, data, data_len);
        
        printf("\nDecryption:\n");
        printf("-----------\n");
        print_hex("Ciphertext", data, data_len);
        
        // Create and initialize cipher for decryption
        cipher_t *cipher_dec;
        err_status_t status;
        
        status = cipher_type_alloc(&aes_cbc, &cipher_dec, 16);
        if (status != err_status_ok) {
            printf("Error: Failed to allocate cipher for decryption\n");
            return 1;
        }

        status = cipher_init(cipher_dec, key, direction_decrypt);
        if (status != err_status_ok) {
            printf("Error: Failed to initialize cipher for decryption\n");
            cipher_dealloc(cipher_dec);
            return 1;
        }

        status = cipher_set_iv(cipher_dec, iv);
        if (status != err_status_ok) {
            printf("Error: Failed to set IV for decryption\n");
            cipher_dealloc(cipher_dec);
            return 1;
        }

        // Decrypt the data
        unsigned int len = data_len;
        status = cipher_decrypt(cipher_dec, result, &len);
        if (status != err_status_ok) {
            printf("Error: Decryption failed\n");
            cipher_dealloc(cipher_dec);
            return 1;
        }

        print_hex("Decrypted", result, len);
        printf("Decrypted text: ");
        for (int i = 0; i < len; i++) {
            if (result[i] >= 32 && result[i] <= 126) {
                printf("%c", result[i]);
            } else {
                printf(".");
            }
        }
        printf("\n");
        printf("Decrypted length: %u bytes\n", len);
        
        cipher_dealloc(cipher_dec);
        
    } else {
        printf("Invalid choice\n");
        return 1;
    }
    
    printf("\nOperation completed successfully!\n");
    return 0;
}
