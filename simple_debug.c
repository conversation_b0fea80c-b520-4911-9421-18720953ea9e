/*
 * simple_debug.c
 * 
 * Simple AES CBC Debug Tool
 * 
 * A streamlined version for quick debugging and testing
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "include/aes_cbc.h"
#include "include/cipher.h"

// External declaration
extern cipher_type_t aes_cbc;

void print_hex(const char* label, const unsigned char* data, int len) {
    printf("%-15s: ", label);
    for (int i = 0; i < len; i++) {
        printf("%02x", data[i]);
        if ((i + 1) % 16 == 0 && i + 1 < len) printf("\n                 ");
        else if ((i + 1) % 4 == 0) printf(" ");
    }
    printf("\n");
}

int aes_cbc_encrypt_decrypt(const unsigned char* key, const unsigned char* iv,
                           const unsigned char* input, int input_len,
                           unsigned char* output, int* output_len,
                           int encrypt) {
    cipher_t *cipher;
    err_status_t status;
    
    // Allocate cipher
    status = cipher_type_alloc(&aes_cbc, &cipher, 16);
    if (status != err_status_ok) {
        printf("Error: Failed to allocate cipher (status: %d)\n", status);
        return -1;
    }
    
    // Initialize cipher
    cipher_direction_t direction = encrypt ? direction_encrypt : direction_decrypt;
    status = cipher_init(cipher, key, direction);
    if (status != err_status_ok) {
        printf("Error: Failed to initialize cipher (status: %d)\n", status);
        cipher_dealloc(cipher);
        return -1;
    }
    
    // Set IV
    status = cipher_set_iv(cipher, (void*)iv);
    if (status != err_status_ok) {
        printf("Error: Failed to set IV (status: %d)\n", status);
        cipher_dealloc(cipher);
        return -1;
    }
    
    // Copy input to output buffer
    memcpy(output, input, input_len);
    *output_len = input_len;
    
    // Perform operation
    if (encrypt) {
        status = cipher_encrypt(cipher, output, (unsigned int*)output_len);
    } else {
        status = cipher_decrypt(cipher, output, (unsigned int*)output_len);
    }
    
    if (status != err_status_ok) {
        printf("Error: %s failed (status: %d)\n", 
               encrypt ? "Encryption" : "Decryption", status);
        cipher_dealloc(cipher);
        return -1;
    }
    
    cipher_dealloc(cipher);
    return 0;
}

void test_aes_cbc(const char* message) {
    printf("\n=== AES CBC Test ===\n");
    printf("Message: \"%s\"\n", message);
    
    // Default key and IV
    unsigned char key[16] = {
        0x2b, 0x7e, 0x15, 0x16, 0x28, 0xae, 0xd2, 0xa6,
        0xab, 0xf7, 0x15, 0x88, 0x09, 0xcf, 0x4f, 0x3c
    };
    
    unsigned char iv[16] = {
        0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
        0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e, 0x0f
    };
    
    int msg_len = strlen(message);
    unsigned char ciphertext[512];
    unsigned char decrypted[512];
    int cipher_len, decrypt_len;
    
    print_hex("Key", key, 16);
    print_hex("IV", iv, 16);
    print_hex("Plaintext", (const unsigned char*)message, msg_len);
    
    // Encrypt
    printf("\n--- Encryption ---\n");
    if (aes_cbc_encrypt_decrypt(key, iv, (const unsigned char*)message, msg_len,
                               ciphertext, &cipher_len, 1) == 0) {
        printf("✓ Encryption successful\n");
        printf("Original length: %d bytes\n", msg_len);
        printf("Encrypted length: %d bytes\n", cipher_len);
        print_hex("Ciphertext", ciphertext, cipher_len);
    } else {
        printf("✗ Encryption failed\n");
        return;
    }
    
    // Decrypt
    printf("\n--- Decryption ---\n");
    if (aes_cbc_encrypt_decrypt(key, iv, ciphertext, cipher_len,
                               decrypted, &decrypt_len, 0) == 0) {
        printf("✓ Decryption successful\n");
        printf("Decrypted length: %d bytes\n", decrypt_len);
        print_hex("Decrypted", decrypted, decrypt_len);
        
        // Verify
        if (decrypt_len == msg_len && memcmp(message, decrypted, msg_len) == 0) {
            printf("✓ Verification: Decrypted text matches original!\n");
            printf("Decrypted message: \"%.*s\"\n", decrypt_len, decrypted);
        } else {
            printf("✗ Verification: Decrypted text does NOT match original!\n");
            printf("Expected: \"%s\"\n", message);
            printf("Got: \"%.*s\"\n", decrypt_len, decrypted);
        }
    } else {
        printf("✗ Decryption failed\n");
    }
}

void interactive_mode() {
    char input[256];
    
    printf("\n=== Interactive Mode ===\n");
    printf("Enter messages to test (empty line to exit):\n");
    
    while (1) {
        printf("\nMessage: ");
        if (!fgets(input, sizeof(input), stdin)) break;
        
        // Remove newline
        input[strcspn(input, "\n")] = 0;
        
        // Exit on empty input
        if (strlen(input) == 0) break;
        
        test_aes_cbc(input);
    }
}

int main(int argc, char* argv[]) {
    printf("Simple AES CBC Debug Tool\n");
    printf("=========================\n");
    
    if (argc > 1) {
        // Test with command line argument
        for (int i = 1; i < argc; i++) {
            test_aes_cbc(argv[i]);
        }
    } else {
        // Default tests
        const char* test_messages[] = {
            "Hello",
            "Hello World!",
            "This is a test message",
            "AES CBC encryption test with a longer message to see padding behavior"
        };
        
        int num_tests = sizeof(test_messages) / sizeof(test_messages[0]);
        
        for (int i = 0; i < num_tests; i++) {
            test_aes_cbc(test_messages[i]);
        }
        
        // Interactive mode
        interactive_mode();
    }
    
    printf("\nDebug session complete!\n");
    return 0;
}
